# Windows 11 Phone Link and Xbox Removal <PERSON>t
# Run this script as Administrator

Write-Host "Windows 11 Phone Link and Xbox Removal Script" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    pause
    exit 1
}

Write-Host "Creating system restore point..." -ForegroundColor Yellow
try {
    Checkpoint-Computer -Description "Before removing Phone Link and Xbox apps" -RestorePointType "MODIFY_SETTINGS"
    Write-Host "System restore point created successfully." -ForegroundColor Green
} catch {
    Write-Host "Warning: Could not create restore point. Continuing anyway..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Removing Phone Link applications..." -ForegroundColor Cyan

# Remove Phone Link / Your Phone apps
$phoneApps = @(
    "*Microsoft.YourPhone*",
    "*PhoneLink*",
    "*Microsoft.WindowsPhone*"
)

foreach ($app in $phoneApps) {
    Write-Host "Searching for: $app" -ForegroundColor White
    $packages = Get-AppxPackage -Name $app -AllUsers
    if ($packages) {
        foreach ($package in $packages) {
            try {
                Write-Host "  Removing: $($package.Name)" -ForegroundColor Yellow
                Remove-AppxPackage -Package $package.PackageFullName -AllUsers
                Write-Host "  Successfully removed: $($package.Name)" -ForegroundColor Green
            } catch {
                Write-Host "  Failed to remove: $($package.Name) - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "  No packages found for: $app" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Removing Xbox applications..." -ForegroundColor Cyan

# Remove Xbox apps
$xboxApps = @(
    "*Microsoft.Xbox*",
    "*Microsoft.XboxApp*",
    "*Microsoft.XboxGameOverlay*",
    "*Microsoft.XboxGamingOverlay*",
    "*Microsoft.XboxIdentityProvider*",
    "*Microsoft.XboxSpeechToTextOverlay*",
    "*Microsoft.XboxGameCallableUI*",
    "*Microsoft.XboxGameBar*"
)

foreach ($app in $xboxApps) {
    Write-Host "Searching for: $app" -ForegroundColor White
    $packages = Get-AppxPackage -Name $app -AllUsers
    if ($packages) {
        foreach ($package in $packages) {
            try {
                Write-Host "  Removing: $($package.Name)" -ForegroundColor Yellow
                Remove-AppxPackage -Package $package.PackageFullName -AllUsers
                Write-Host "  Successfully removed: $($package.Name)" -ForegroundColor Green
            } catch {
                Write-Host "  Failed to remove: $($package.Name) - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "  No packages found for: $app" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "Disabling Xbox Game Bar via Registry..." -ForegroundColor Cyan

# Disable Xbox Game Bar
try {
    $regPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\GameDVR"
    if (!(Test-Path $regPath)) {
        New-Item -Path $regPath -Force | Out-Null
    }
    Set-ItemProperty -Path $regPath -Name "AppCaptureEnabled" -Value 0 -Type DWord
    Set-ItemProperty -Path $regPath -Name "GameDVR_Enabled" -Value 0 -Type DWord
    
    $regPath2 = "HKCU:\System\GameConfigStore"
    if (!(Test-Path $regPath2)) {
        New-Item -Path $regPath2 -Force | Out-Null
    }
    Set-ItemProperty -Path $regPath2 -Name "GameDVR_Enabled" -Value 0 -Type DWord
    
    Write-Host "Xbox Game Bar disabled via registry." -ForegroundColor Green
} catch {
    Write-Host "Failed to disable Xbox Game Bar via registry: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Removing provisioned packages (prevents reinstall)..." -ForegroundColor Cyan

# Remove provisioned packages to prevent reinstallation
$provisionedApps = @(
    "*Microsoft.YourPhone*",
    "*Microsoft.Xbox*"
)

foreach ($app in $provisionedApps) {
    Write-Host "Searching for provisioned: $app" -ForegroundColor White
    $packages = Get-AppxProvisionedPackage -Online | Where-Object {$_.DisplayName -like $app}
    if ($packages) {
        foreach ($package in $packages) {
            try {
                Write-Host "  Removing provisioned: $($package.DisplayName)" -ForegroundColor Yellow
                Remove-AppxProvisionedPackage -Online -PackageName $package.PackageName
                Write-Host "  Successfully removed provisioned: $($package.DisplayName)" -ForegroundColor Green
            } catch {
                Write-Host "  Failed to remove provisioned: $($package.DisplayName) - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "  No provisioned packages found for: $app" -ForegroundColor Gray
    }
}

Write-Host ""
Write-Host "=============================================" -ForegroundColor Green
Write-Host "Removal process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Notes:" -ForegroundColor Yellow
Write-Host "- Some Xbox components may be required by other Windows features" -ForegroundColor White
Write-Host "- Apps may reinstall during major Windows updates" -ForegroundColor White
Write-Host "- A system restart is recommended" -ForegroundColor White
Write-Host "- If you experience issues, use System Restore" -ForegroundColor White
Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Cyan
pause
