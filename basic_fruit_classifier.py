import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os

# Configuration
DATASET_PATH = "FruitDetection"
IMG_HEIGHT = 150
IMG_WIDTH = 150
BATCH_SIZE = 32

def load_dataset():
    """Load the dataset using tf.keras.utils.image_dataset_from_directory"""
    print("Loading dataset...")
    
    # Load training data
    train_ds = tf.keras.utils.image_dataset_from_directory(
        os.path.join(DATASET_PATH, 'train'),
        validation_split=0.2,
        subset="training",
        seed=123,
        image_size=(IMG_HEIGHT, IMG_WIDTH),
        batch_size=BATCH_SIZE
    )
    
    # Load validation data
    val_ds = tf.keras.utils.image_dataset_from_directory(
        os.path.join(DATASET_PATH, 'train'),
        validation_split=0.2,
        subset="validation",
        seed=123,
        image_size=(IMG_HEIGHT, IMG_WIDTH),
        batch_size=BATCH_SIZE
    )
    
    # Load test data
    test_ds = tf.keras.utils.image_dataset_from_directory(
        os.path.join(DATASET_PATH, 'test'),
        seed=123,
        image_size=(IMG_HEIGHT, IMG_WIDTH),
        batch_size=BATCH_SIZE
    )
    
    # Get class names
    class_names = train_ds.class_names
    print(f"Class names: {class_names}")
    
    return train_ds, val_ds, test_ds, class_names

def normalize_data(train_ds, val_ds, test_ds):
    """Normalize pixel values to [0,1]"""
    normalization_layer = tf.keras.layers.Rescaling(1./255)
    
    train_ds = train_ds.map(lambda x, y: (normalization_layer(x), y))
    val_ds = val_ds.map(lambda x, y: (normalization_layer(x), y))
    test_ds = test_ds.map(lambda x, y: (normalization_layer(x), y))
    
    return train_ds, val_ds, test_ds

def create_model(num_classes):
    """Create a simple CNN model"""
    print("Creating CNN model...")
    
    model = tf.keras.Sequential([
        tf.keras.layers.Rescaling(1./255, input_shape=(IMG_HEIGHT, IMG_WIDTH, 3)),
        tf.keras.layers.Conv2D(16, 3, padding='same', activation='relu'),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Conv2D(32, 3, padding='same', activation='relu'),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Conv2D(64, 3, padding='same', activation='relu'),
        tf.keras.layers.MaxPooling2D(),
        tf.keras.layers.Flatten(),
        tf.keras.layers.Dense(128, activation='relu'),
        tf.keras.layers.Dense(num_classes, activation='softmax' if num_classes > 2 else 'sigmoid')
    ])
    
    # Compile model
    model.compile(
        optimizer='adam',
        loss='sparse_categorical_crossentropy' if num_classes > 2 else 'binary_crossentropy',
        metrics=['accuracy']
    )
    
    print("Model created successfully!")
    model.summary()
    
    return model

def train_model(model, train_ds, val_ds, epochs=10):
    """Train the model"""
    print(f"Training model for {epochs} epochs...")
    
    history = model.fit(
        train_ds,
        validation_data=val_ds,
        epochs=epochs,
        verbose=1
    )
    
    return history

def plot_training_history(history):
    """Plot training history"""
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    
    epochs_range = range(len(acc))
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(epochs_range, acc, label='Training Accuracy')
    plt.plot(epochs_range, val_acc, label='Validation Accuracy')
    plt.legend(loc='lower right')
    plt.title('Training and Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    
    plt.subplot(1, 2, 2)
    plt.plot(epochs_range, loss, label='Training Loss')
    plt.plot(epochs_range, val_loss, label='Validation Loss')
    plt.legend(loc='upper right')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    
    plt.tight_layout()
    plt.show()

def evaluate_model(model, test_ds):
    """Evaluate model on test data"""
    print("Evaluating model on test data...")
    
    test_loss, test_acc = model.evaluate(test_ds, verbose=1)
    print(f"Test accuracy: {test_acc:.4f}")
    
    return test_acc

def predict_sample_images(model, test_ds, class_names, num_samples=9):
    """Predict and display sample images"""
    print(f"Predicting {num_samples} sample images...")
    
    # Get a batch of test images
    for images, labels in test_ds.take(1):
        predictions = model.predict(images)
        
        plt.figure(figsize=(12, 12))
        for i in range(min(num_samples, len(images))):
            plt.subplot(3, 3, i + 1)
            plt.imshow(images[i].numpy().astype("uint8"))
            
            if len(class_names) == 2:
                # Binary classification
                predicted_class = class_names[1] if predictions[i] > 0.5 else class_names[0]
                confidence = predictions[i][0] if predictions[i] > 0.5 else 1 - predictions[i][0]
                true_class = class_names[labels[i]]
            else:
                # Multi-class classification
                predicted_class = class_names[np.argmax(predictions[i])]
                confidence = np.max(predictions[i])
                true_class = class_names[labels[i]]
            
            plt.title(f"True: {true_class}\nPred: {predicted_class}\nConf: {confidence:.2f}")
            plt.axis('off')
        
        plt.tight_layout()
        plt.show()
        break

def show_sample_images(train_ds, class_names):
    """Show sample images from the dataset"""
    print("Showing sample images from dataset...")
    
    plt.figure(figsize=(10, 10))
    for images, labels in train_ds.take(1):
        for i in range(9):
            ax = plt.subplot(3, 3, i + 1)
            plt.imshow(images[i].numpy().astype("uint8"))
            plt.title(class_names[labels[i]])
            plt.axis("off")
    plt.show()

def main():
    """Main function"""
    print("🍍 Basic Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 50)
    
    # Set random seed for reproducibility
    tf.random.set_seed(42)
    
    # Load dataset
    train_ds, val_ds, test_ds, class_names = load_dataset()
    
    # Show sample images
    show_sample_images(train_ds, class_names)
    
    # Create model
    num_classes = len(class_names)
    model = create_model(num_classes)
    
    # Train model
    history = train_model(model, train_ds, val_ds, epochs=8)
    
    # Plot training history
    plot_training_history(history)
    
    # Evaluate model
    test_acc = evaluate_model(model, test_ds)
    
    # Show predictions on sample images
    predict_sample_images(model, test_ds, class_names)
    
    # Save model
    model.save("basic_fruit_classifier.h5")
    print("Model saved as 'basic_fruit_classifier.h5'")
    
    print(f"\n🎉 Training completed! Final test accuracy: {test_acc:.4f}")

if __name__ == "__main__":
    main()
