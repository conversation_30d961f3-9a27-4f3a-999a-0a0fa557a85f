import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image

# Disable the problematic optree functionality
import warnings
warnings.filterwarnings('ignore')

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 150
BATCH_SIZE = 32

def load_images_from_folder(folder_path, label, max_images=None):
    """Load images from a folder and assign labels"""
    images = []
    labels = []
    
    image_files = os.listdir(folder_path)
    if max_images:
        image_files = image_files[:max_images]
    
    for filename in image_files:
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                # Load and resize image
                img = Image.open(img_path)
                img = img.convert('RGB')
                img = img.resize((IMG_SIZE, IMG_SIZE))
                img_array = np.array(img) / 255.0  # Normalize to [0,1]
                
                images.append(img_array)
                labels.append(label)
            except Exception as e:
                print(f"Error loading {filename}: {e}")
    
    return np.array(images), np.array(labels)

def load_dataset():
    """Load the complete dataset"""
    print("Loading dataset...")
    
    # Load training data
    train_corn_images, train_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'corn'), 0
    )
    train_pineapple_images, train_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1
    )
    
    # Load validation data
    val_corn_images, val_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'corn'), 0
    )
    val_pineapple_images, val_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1
    )
    
    # Load test data
    test_corn_images, test_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'corn'), 0
    )
    test_pineapple_images, test_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1
    )
    
    # Combine data
    X_train = np.concatenate([train_corn_images, train_pineapple_images])
    y_train = np.concatenate([train_corn_labels, train_pineapple_labels])
    
    X_val = np.concatenate([val_corn_images, val_pineapple_images])
    y_val = np.concatenate([val_corn_labels, val_pineapple_labels])
    
    X_test = np.concatenate([test_corn_images, test_pineapple_images])
    y_test = np.concatenate([test_corn_labels, test_pineapple_labels])
    
    # Shuffle training data
    train_indices = np.random.permutation(len(X_train))
    X_train = X_train[train_indices]
    y_train = y_train[train_indices]
    
    print(f"Training set: {len(X_train)} images")
    print(f"Validation set: {len(X_val)} images")
    print(f"Test set: {len(X_test)} images")
    
    return X_train, y_train, X_val, y_val, X_test, y_test

def create_model_step_by_step():
    """Create model step by step to avoid compatibility issues"""
    print("Creating CNN model step by step...")
    
    try:
        # Create model using the older approach
        model = tf.keras.models.Sequential()
        
        # Add layers one by one
        model.add(tf.keras.layers.InputLayer(input_shape=(IMG_SIZE, IMG_SIZE, 3)))
        model.add(tf.keras.layers.Conv2D(32, (3, 3), activation='relu'))
        model.add(tf.keras.layers.MaxPooling2D((2, 2)))
        
        model.add(tf.keras.layers.Conv2D(64, (3, 3), activation='relu'))
        model.add(tf.keras.layers.MaxPooling2D((2, 2)))
        
        model.add(tf.keras.layers.Conv2D(128, (3, 3), activation='relu'))
        model.add(tf.keras.layers.MaxPooling2D((2, 2)))
        
        model.add(tf.keras.layers.Flatten())
        model.add(tf.keras.layers.Dense(128, activation='relu'))
        model.add(tf.keras.layers.Dropout(0.5))
        model.add(tf.keras.layers.Dense(1, activation='sigmoid'))
        
        # Compile model
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        print("Model created successfully!")
        print(f"Model has {model.count_params():,} parameters")
        
        return model
        
    except Exception as e:
        print(f"Error creating model: {e}")
        return None

def train_model(model, X_train, y_train, X_val, y_val, epochs=8):
    """Train the model"""
    if model is None:
        print("No model to train!")
        return None
        
    print(f"Training model for {epochs} epochs...")
    
    try:
        history = model.fit(
            X_train, y_train,
            batch_size=BATCH_SIZE,
            epochs=epochs,
            validation_data=(X_val, y_val),
            verbose=1
        )
        return history
    except Exception as e:
        print(f"Error during training: {e}")
        return None

def plot_training_history(history):
    """Plot training history"""
    if history is None:
        print("No training history to plot!")
        return
        
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    
    epochs_range = range(len(acc))
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(epochs_range, acc, label='Training Accuracy')
    plt.plot(epochs_range, val_acc, label='Validation Accuracy')
    plt.legend(loc='lower right')
    plt.title('Training and Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(epochs_range, loss, label='Training Loss')
    plt.plot(epochs_range, val_loss, label='Validation Loss')
    plt.legend(loc='upper right')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

def evaluate_model(model, X_test, y_test):
    """Evaluate model on test data"""
    if model is None:
        print("No model to evaluate!")
        return 0
        
    print("Evaluating model on test data...")
    
    try:
        test_loss, test_acc = model.evaluate(X_test, y_test, verbose=1)
        print(f"Test accuracy: {test_acc:.4f}")
        print(f"Test loss: {test_loss:.4f}")
        return test_acc
    except Exception as e:
        print(f"Error during evaluation: {e}")
        return 0

def show_sample_images(X_train, y_train, class_names=['corn', 'pineapple']):
    """Show sample images from training set"""
    print("Showing sample images from training set...")
    
    plt.figure(figsize=(10, 8))
    
    # Show 4 corn images
    corn_indices = np.where(y_train == 0)[0][:4]
    for i, idx in enumerate(corn_indices):
        plt.subplot(2, 4, i + 1)
        plt.imshow(X_train[idx])
        plt.title(f'{class_names[0].capitalize()}')
        plt.axis('off')
    
    # Show 4 pineapple images
    pineapple_indices = np.where(y_train == 1)[0][:4]
    for i, idx in enumerate(pineapple_indices):
        plt.subplot(2, 4, i + 5)
        plt.imshow(X_train[idx])
        plt.title(f'{class_names[1].capitalize()}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def test_predictions(model, X_test, y_test, num_tests=10):
    """Test predictions and show accuracy"""
    if model is None:
        print("No model for predictions!")
        return
        
    print(f"Testing {num_tests} random predictions...")
    
    # Get random samples
    indices = np.random.choice(len(X_test), num_tests, replace=False)
    correct = 0
    
    class_names = ['corn', 'pineapple']
    
    for i, idx in enumerate(indices):
        # Make prediction
        prediction = model.predict(X_test[idx:idx+1], verbose=0)[0][0]
        predicted_class = class_names[1] if prediction > 0.5 else class_names[0]
        confidence = prediction if prediction > 0.5 else 1 - prediction
        true_class = class_names[y_test[idx]]
        
        is_correct = predicted_class == true_class
        if is_correct:
            correct += 1
            
        print(f"Test {i+1:2d}: True={true_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")
    
    accuracy = correct / num_tests
    print(f"\nRandom test accuracy: {accuracy:.2f} ({correct}/{num_tests})")

def main():
    """Main function"""
    print("🍍 Working Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 50)
    
    # Set random seed
    tf.random.set_seed(42)
    np.random.seed(42)
    
    try:
        # Load dataset
        X_train, y_train, X_val, y_val, X_test, y_test = load_dataset()
        
        # Show sample images
        show_sample_images(X_train, y_train)
        
        # Create model
        model = create_model_step_by_step()
        
        if model is not None:
            # Train model
            history = train_model(model, X_train, y_train, X_val, y_val, epochs=6)
            
            # Plot training history
            plot_training_history(history)
            
            # Evaluate model
            test_acc = evaluate_model(model, X_test, y_test)
            
            # Test random predictions
            test_predictions(model, X_test, y_test)
            
            # Save model
            try:
                model.save("working_fruit_classifier.h5")
                print("Model saved as 'working_fruit_classifier.h5'")
            except Exception as e:
                print(f"Could not save model: {e}")
            
            print(f"\n🎉 Training completed! Final test accuracy: {test_acc:.4f}")
        else:
            print("❌ Could not create model due to compatibility issues.")
            
    except Exception as e:
        print(f"Error in main function: {e}")

if __name__ == "__main__":
    main()
