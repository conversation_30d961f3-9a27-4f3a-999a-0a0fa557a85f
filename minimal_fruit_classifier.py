import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 150
BATCH_SIZE = 32

def load_images_from_folder(folder_path, label, max_images=None):
    """Load images from a folder and assign labels"""
    images = []
    labels = []
    
    image_files = os.listdir(folder_path)
    if max_images:
        image_files = image_files[:max_images]
    
    for filename in image_files:
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                # Load and resize image
                img = Image.open(img_path)
                img = img.convert('RGB')
                img = img.resize((IMG_SIZE, IMG_SIZE))
                img_array = np.array(img) / 255.0  # Normalize to [0,1]
                
                images.append(img_array)
                labels.append(label)
            except Exception as e:
                print(f"Error loading {filename}: {e}")
    
    return np.array(images), np.array(labels)

def load_dataset():
    """Load the complete dataset"""
    print("Loading dataset...")
    
    # Load training data
    train_corn_images, train_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'corn'), 0
    )
    train_pineapple_images, train_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1
    )
    
    # Load validation data
    val_corn_images, val_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'corn'), 0
    )
    val_pineapple_images, val_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1
    )
    
    # Load test data
    test_corn_images, test_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'corn'), 0
    )
    test_pineapple_images, test_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1
    )
    
    # Combine data
    X_train = np.concatenate([train_corn_images, train_pineapple_images])
    y_train = np.concatenate([train_corn_labels, train_pineapple_labels])
    
    X_val = np.concatenate([val_corn_images, val_pineapple_images])
    y_val = np.concatenate([val_corn_labels, val_pineapple_labels])
    
    X_test = np.concatenate([test_corn_images, test_pineapple_images])
    y_test = np.concatenate([test_corn_labels, test_pineapple_labels])
    
    # Shuffle training data
    train_indices = np.random.permutation(len(X_train))
    X_train = X_train[train_indices]
    y_train = y_train[train_indices]
    
    print(f"Training set: {len(X_train)} images")
    print(f"Validation set: {len(X_val)} images")
    print(f"Test set: {len(X_test)} images")
    
    return X_train, y_train, X_val, y_val, X_test, y_test

def create_simple_model():
    """Create a simple CNN model using functional API"""
    print("Creating simple CNN model...")
    
    # Input layer
    inputs = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))
    
    # Convolutional layers
    x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(inputs)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    
    x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    
    x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    
    # Flatten and dense layers
    x = tf.keras.layers.Flatten()(x)
    x = tf.keras.layers.Dense(128, activation='relu')(x)
    x = tf.keras.layers.Dropout(0.5)(x)
    outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)
    
    # Create model
    model = tf.keras.Model(inputs=inputs, outputs=outputs)
    
    # Compile model
    model.compile(
        optimizer='adam',
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    print("Model created successfully!")
    model.summary()
    
    return model

def train_model(model, X_train, y_train, X_val, y_val, epochs=10):
    """Train the model"""
    print(f"Training model for {epochs} epochs...")
    
    history = model.fit(
        X_train, y_train,
        batch_size=BATCH_SIZE,
        epochs=epochs,
        validation_data=(X_val, y_val),
        verbose=1
    )
    
    return history

def plot_training_history(history):
    """Plot training history"""
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    
    epochs_range = range(len(acc))
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(epochs_range, acc, label='Training Accuracy')
    plt.plot(epochs_range, val_acc, label='Validation Accuracy')
    plt.legend(loc='lower right')
    plt.title('Training and Validation Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.grid(True)
    
    plt.subplot(1, 2, 2)
    plt.plot(epochs_range, loss, label='Training Loss')
    plt.plot(epochs_range, val_loss, label='Validation Loss')
    plt.legend(loc='upper right')
    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    plt.tight_layout()
    plt.show()

def evaluate_model(model, X_test, y_test):
    """Evaluate model on test data"""
    print("Evaluating model on test data...")
    
    test_loss, test_acc = model.evaluate(X_test, y_test, verbose=1)
    print(f"Test accuracy: {test_acc:.4f}")
    print(f"Test loss: {test_loss:.4f}")
    
    return test_acc

def show_predictions(model, X_test, y_test, class_names=['corn', 'pineapple'], num_samples=9):
    """Show predictions on test images"""
    print(f"Showing predictions on {num_samples} test images...")
    
    # Get random samples
    indices = np.random.choice(len(X_test), num_samples, replace=False)
    
    plt.figure(figsize=(12, 12))
    for i, idx in enumerate(indices):
        plt.subplot(3, 3, i + 1)
        plt.imshow(X_test[idx])
        
        # Make prediction
        prediction = model.predict(X_test[idx:idx+1], verbose=0)[0][0]
        predicted_class = class_names[1] if prediction > 0.5 else class_names[0]
        confidence = prediction if prediction > 0.5 else 1 - prediction
        true_class = class_names[y_test[idx]]
        
        # Color: green if correct, red if wrong
        color = 'green' if predicted_class == true_class else 'red'
        
        plt.title(f"True: {true_class}\nPred: {predicted_class}\nConf: {confidence:.2f}", 
                 color=color)
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def show_sample_images(X_train, y_train, class_names=['corn', 'pineapple']):
    """Show sample images from training set"""
    print("Showing sample images from training set...")
    
    plt.figure(figsize=(10, 8))
    
    # Show 4 corn images
    corn_indices = np.where(y_train == 0)[0][:4]
    for i, idx in enumerate(corn_indices):
        plt.subplot(2, 4, i + 1)
        plt.imshow(X_train[idx])
        plt.title(f'{class_names[0].capitalize()}')
        plt.axis('off')
    
    # Show 4 pineapple images
    pineapple_indices = np.where(y_train == 1)[0][:4]
    for i, idx in enumerate(pineapple_indices):
        plt.subplot(2, 4, i + 5)
        plt.imshow(X_train[idx])
        plt.title(f'{class_names[1].capitalize()}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def main():
    """Main function"""
    print("🍍 Minimal Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 50)
    
    # Set random seed
    tf.random.set_seed(42)
    np.random.seed(42)
    
    # Load dataset
    X_train, y_train, X_val, y_val, X_test, y_test = load_dataset()
    
    # Show sample images
    show_sample_images(X_train, y_train)
    
    # Create model
    model = create_simple_model()
    
    # Train model
    history = train_model(model, X_train, y_train, X_val, y_val, epochs=8)
    
    # Plot training history
    plot_training_history(history)
    
    # Evaluate model
    test_acc = evaluate_model(model, X_test, y_test)
    
    # Show predictions
    show_predictions(model, X_test, y_test)
    
    # Save model
    model.save("minimal_fruit_classifier.h5")
    print("Model saved as 'minimal_fruit_classifier.h5'")
    
    print(f"\n🎉 Training completed! Final test accuracy: {test_acc:.4f}")

if __name__ == "__main__":
    main()
