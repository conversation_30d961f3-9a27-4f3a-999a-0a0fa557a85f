{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🍍 Fruit Classifier - Corn vs Pineapple 🌽\n", "\n", "A TensorFlow-based image classifier to distinguish between corn and pineapple images."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Import Libraries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import tensorflow as tf\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import os\n", "from PIL import Image\n", "from sklearn.metrics import classification_report, confusion_matrix\n", "import seaborn as sns\n", "\n", "print(\"🔧 Using TensorFlow for image classification\")\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "\n", "# Configuration\n", "DATASET_PATH = \"FruitDetection\"\n", "IMG_SIZE = 150\n", "CLASS_NAMES = ['corn', 'pineapple']\n", "\n", "# Set random seeds\n", "tf.random.set_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_images_from_folder(folder_path, label):\n", "    \"\"\"Load images from a folder and assign labels\"\"\"\n", "    images = []\n", "    labels = []\n", "    \n", "    image_files = os.listdir(folder_path)\n", "    print(f\"Loading {len(image_files)} images from {folder_path}...\")\n", "    \n", "    for i, filename in enumerate(image_files):\n", "        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):\n", "            img_path = os.path.join(folder_path, filename)\n", "            try:\n", "                img = Image.open(img_path)\n", "                img = img.convert('RGB')\n", "                img = img.resize((IMG_SIZE, IMG_SIZE))\n", "                img_array = np.array(img) / 255.0  # Normalize to [0,1]\n", "                \n", "                images.append(img_array)\n", "                labels.append(label)\n", "                \n", "                if (i + 1) % 100 == 0:\n", "                    print(f\"  Loaded {i + 1}/{len(image_files)} images\")\n", "                    \n", "            except Exception as e:\n", "                print(f\"Error loading {filename}: {e}\")\n", "    \n", "    return np.array(images), np.array(labels)\n", "\n", "def load_dataset():\n", "    \"\"\"Load the complete dataset\"\"\"\n", "    print(\"🍍 Loading fruit classification dataset...\")\n", "\n", "    # Load training data (train + val)\n", "    train_corn_images, train_corn_labels = load_images_from_folder(\n", "        os.path.join(DATASET_PATH, 'train', 'corn'), 0\n", "    )\n", "    train_pineapple_images, train_pineapple_labels = load_images_from_folder(\n", "        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1\n", "    )\n", "    \n", "    val_corn_images, val_corn_labels = load_images_from_folder(\n", "        os.path.join(DATASET_PATH, 'val', 'corn'), 0\n", "    )\n", "    val_pineapple_images, val_pineapple_labels = load_images_from_folder(\n", "        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1\n", "    )\n", "    \n", "    # Load test data\n", "    test_corn_images, test_corn_labels = load_images_from_folder(\n", "        os.path.join(DATASET_PATH, 'test', 'corn'), 0\n", "    )\n", "    test_pineapple_images, test_pineapple_labels = load_images_from_folder(\n", "        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1\n", "    )\n", "    \n", "    # Combine training and validation data\n", "    X_train = np.concatenate([train_corn_images, train_pineapple_images, \n", "                             val_corn_images, val_pineapple_images])\n", "    y_train = np.concatenate([train_corn_labels, train_pineapple_labels,\n", "                             val_corn_labels, val_pineapple_labels])\n", "    \n", "    X_test = np.concatenate([test_corn_images, test_pineapple_images])\n", "    y_test = np.concatenate([test_corn_labels, test_pineapple_labels])\n", "    \n", "    # Shuffle training data\n", "    train_indices = np.random.permutation(len(X_train))\n", "    X_train = X_train[train_indices]\n", "    y_train = y_train[train_indices]\n", "    \n", "    print(f\"Training set: {len(X_train)} images\")\n", "    print(f\"Test set: {len(X_test)} images\")\n", "    \n", "    return X_train, y_train, X_test, y_test"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Load Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the dataset\n", "X_train, y_train, X_test, y_test = load_dataset()\n", "\n", "print(f\"\\nDataset shapes:\")\n", "print(f\"X_train: {X_train.shape}\")\n", "print(f\"y_train: {y_train.shape}\")\n", "print(f\"X_test: {X_test.shape}\")\n", "print(f\"y_test: {y_test.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Create CNN Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_cnn_model():\n", "    \"\"\"Create a simple CNN model using TensorFlow\"\"\"\n", "    print(\"🏗️ Creating CNN model...\")\n", "    \n", "    # Use functional API\n", "    inputs = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))\n", "    \n", "    # First convolutional block\n", "    x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(inputs)\n", "    x = tf.keras.layers.MaxPooling2D((2, 2))(x)\n", "    \n", "    # Second convolutional block\n", "    x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)\n", "    x = tf.keras.layers.MaxPooling2D((2, 2))(x)\n", "    \n", "    # Third convolutional block\n", "    x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)\n", "    x = tf.keras.layers.MaxPooling2D((2, 2))(x)\n", "    \n", "    # Flatten and dense layers\n", "    x = tf.keras.layers.Flatten()(x)\n", "    x = tf.keras.layers.Dense(128, activation='relu')(x)\n", "    x = tf.keras.layers.Dropout(0.5)(x)\n", "    outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)\n", "    \n", "    model = tf.keras.Model(inputs=inputs, outputs=outputs)\n", "    \n", "    # Compile model\n", "    model.compile(\n", "        optimizer='adam',\n", "        loss='binary_crossentropy',\n", "        metrics=['accuracy']\n", "    )\n", "    \n", "    print(\"✅ Model created!\")\n", "    return model\n", "\n", "# Create the model\n", "model = create_cnn_model()\n", "model.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON> the Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"🚀 Training CNN classifier...\")\n", "\n", "# Train model\n", "history = model.fit(\n", "    X_train, y_train,\n", "    batch_size=32,\n", "    epochs=10,\n", "    validation_split=0.2,\n", "    verbose=1\n", ")\n", "\n", "print(\"✅ Training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Plot Training History"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot training history\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.plot(history.history['accuracy'], label='Training Accuracy')\n", "plt.plot(history.history['val_accuracy'], label='Validation Accuracy')\n", "plt.title('Model Accuracy')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.plot(history.history['loss'], label='Training Loss')\n", "plt.plot(history.history['val_loss'], label='Validation Loss')\n", "plt.title('Model Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"📊 Evaluating classifier...\")\n", "\n", "# Get predictions\n", "y_pred_proba = model.predict(X_test)\n", "y_pred = (y_pred_proba > 0.5).astype(int).flatten()\n", "\n", "# Calculate accuracy\n", "accuracy = np.mean(y_pred == y_test)\n", "\n", "print(f\"Test Accuracy: {accuracy:.4f}\")\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=CLASS_NAMES))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(y_test, y_pred)\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "           xticklabels=CLASS_NAMES, yticklabels=CLASS_NAMES)\n", "plt.title('Confusion Matrix')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Test Random Predictions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_random_predictions(model, X_test, y_test, num_tests=10):\n", "    \"\"\"Test random predictions\"\"\"\n", "    print(f\"🔍 Testing {num_tests} random predictions...\")\n", "    \n", "    indices = np.random.choice(len(X_test), num_tests, replace=False)\n", "    correct = 0\n", "    \n", "    for i, idx in enumerate(indices):\n", "        # Get prediction probability\n", "        input_data = np.expand_dims(X_test[idx], axis=0)\n", "        prediction_proba = model.predict(input_data, verbose=0)[0][0]\n", "        prediction = 1 if prediction_proba > 0.5 else 0\n", "        predicted_class = CLASS_NAMES[prediction]\n", "        true_class = CLASS_NAMES[y_test[idx]]\n", "        \n", "        # Get confidence\n", "        confidence = prediction_proba if prediction_proba > 0.5 else 1 - prediction_proba\n", "        \n", "        is_correct = predicted_class == true_class\n", "        if is_correct:\n", "            correct += 1\n", "            \n", "        print(f\"Test {i+1:2d}: True={true_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}\")\n", "    \n", "    accuracy = correct / num_tests\n", "    print(f\"\\nRandom test accuracy: {accuracy:.2f} ({correct}/{num_tests})\")\n", "    return accuracy\n", "\n", "# Test random predictions\n", "test_random_predictions(model, X_test, y_test, num_tests=15)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. <PERSON> Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save model\n", "model.save(\"fruit_classifier_cnn.h5\")\n", "print(\"Model saved as 'fruit_classifier_cnn.h5'\")\n", "\n", "print(f\"\\n🎉 TensorFlow fruit classifier completed! Final accuracy: {accuracy:.4f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}