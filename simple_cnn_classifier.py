import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import ImageDataGenerator

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = (150, 150)  # Smaller size for faster training
BATCH_SIZE = 32
CLASS_NAMES = ['corn', 'pineapple']

def create_data_generators():
    """Create data generators for training, validation, and testing"""
    print("Creating data generators...")
    
    # Data augmentation for training
    train_datagen = ImageDataGenerator(
        rescale=1./255,
        rotation_range=20,
        width_shift_range=0.2,
        height_shift_range=0.2,
        horizontal_flip=True,
        zoom_range=0.2
    )
    
    # No augmentation for validation and test
    val_test_datagen = ImageDataGenerator(rescale=1./255)
    
    # Create generators
    train_generator = train_datagen.flow_from_directory(
        os.path.join(DATASET_PATH, 'train'),
        target_size=IMG_SIZE,
        batch_size=BATCH_SIZE,
        class_mode='binary'
    )
    
    val_generator = val_test_datagen.flow_from_directory(
        os.path.join(DATASET_PATH, 'val'),
        target_size=IMG_SIZE,
        batch_size=BATCH_SIZE,
        class_mode='binary'
    )
    
    test_generator = val_test_datagen.flow_from_directory(
        os.path.join(DATASET_PATH, 'test'),
        target_size=IMG_SIZE,
        batch_size=BATCH_SIZE,
        class_mode='binary',
        shuffle=False
    )
    
    print(f"Training samples: {train_generator.samples}")
    print(f"Validation samples: {val_generator.samples}")
    print(f"Test samples: {test_generator.samples}")
    
    return train_generator, val_generator, test_generator

def build_cnn_model():
    """Build a simple CNN model"""
    print("Building CNN model...")
    
    model = tf.keras.models.Sequential()
    
    # First convolutional layer
    model.add(tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(*IMG_SIZE, 3)))
    model.add(tf.keras.layers.MaxPooling2D(2, 2))
    
    # Second convolutional layer
    model.add(tf.keras.layers.Conv2D(64, (3, 3), activation='relu'))
    model.add(tf.keras.layers.MaxPooling2D(2, 2))
    
    # Third convolutional layer
    model.add(tf.keras.layers.Conv2D(128, (3, 3), activation='relu'))
    model.add(tf.keras.layers.MaxPooling2D(2, 2))
    
    # Fourth convolutional layer
    model.add(tf.keras.layers.Conv2D(128, (3, 3), activation='relu'))
    model.add(tf.keras.layers.MaxPooling2D(2, 2))
    
    # Flatten and dense layers
    model.add(tf.keras.layers.Flatten())
    model.add(tf.keras.layers.Dropout(0.5))
    model.add(tf.keras.layers.Dense(512, activation='relu'))
    model.add(tf.keras.layers.Dense(1, activation='sigmoid'))
    
    # Compile model
    model.compile(
        optimizer='adam',
        loss='binary_crossentropy',
        metrics=['accuracy']
    )
    
    print("Model built successfully!")
    print(f"Total parameters: {model.count_params():,}")
    
    return model

def train_model(model, train_gen, val_gen, epochs=10):
    """Train the model"""
    print(f"Training model for {epochs} epochs...")
    
    # Define callbacks
    early_stopping = tf.keras.callbacks.EarlyStopping(
        monitor='val_loss',
        patience=3,
        restore_best_weights=True
    )
    
    reduce_lr = tf.keras.callbacks.ReduceLROnPlateau(
        monitor='val_loss',
        factor=0.2,
        patience=2,
        min_lr=0.0001
    )
    
    # Train the model
    history = model.fit(
        train_gen,
        epochs=epochs,
        validation_data=val_gen,
        callbacks=[early_stopping, reduce_lr],
        verbose=1
    )
    
    return history

def plot_training_results(history):
    """Plot training accuracy and loss"""
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    
    epochs_range = range(len(acc))
    
    plt.figure(figsize=(12, 4))
    
    plt.subplot(1, 2, 1)
    plt.plot(epochs_range, acc, label='Training Accuracy')
    plt.plot(epochs_range, val_acc, label='Validation Accuracy')
    plt.legend(loc='lower right')
    plt.title('Training and Validation Accuracy')
    
    plt.subplot(1, 2, 2)
    plt.plot(epochs_range, loss, label='Training Loss')
    plt.plot(epochs_range, val_loss, label='Validation Loss')
    plt.legend(loc='upper right')
    plt.title('Training and Validation Loss')
    
    plt.show()

def evaluate_model(model, test_gen):
    """Evaluate the model on test data"""
    print("Evaluating model on test data...")
    
    test_loss, test_acc = model.evaluate(test_gen, verbose=1)
    print(f"Test accuracy: {test_acc:.4f}")
    print(f"Test loss: {test_loss:.4f}")
    
    return test_acc, test_loss

def predict_image(model, image_path):
    """Predict a single image"""
    # Load and preprocess image
    img = tf.keras.preprocessing.image.load_img(image_path, target_size=IMG_SIZE)
    img_array = tf.keras.preprocessing.image.img_to_array(img)
    img_array = np.expand_dims(img_array, axis=0) / 255.0
    
    # Make prediction
    prediction = model.predict(img_array)[0][0]
    predicted_class = CLASS_NAMES[1] if prediction > 0.5 else CLASS_NAMES[0]
    confidence = prediction if prediction > 0.5 else 1 - prediction
    
    # Display result
    plt.figure(figsize=(6, 6))
    plt.imshow(img)
    plt.title(f'Prediction: {predicted_class} (Confidence: {confidence:.2f})')
    plt.axis('off')
    plt.show()
    
    return predicted_class, confidence

def test_random_predictions(model, num_tests=5):
    """Test random predictions from test set"""
    print(f"Testing {num_tests} random predictions...")
    
    test_path = os.path.join(DATASET_PATH, 'test')
    classes = ['corn', 'pineapple']
    correct = 0
    
    for i in range(num_tests):
        # Select random image
        random_class = np.random.choice(classes)
        class_path = os.path.join(test_path, random_class)
        image_files = os.listdir(class_path)
        random_image = np.random.choice(image_files)
        image_path = os.path.join(class_path, random_image)
        
        # Make prediction
        predicted_class, confidence = predict_image(model, image_path)
        
        # Check if correct
        is_correct = predicted_class == random_class
        if is_correct:
            correct += 1
            
        print(f"Test {i+1}: True={random_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")
    
    accuracy = correct / num_tests
    print(f"Random test accuracy: {accuracy:.2f} ({correct}/{num_tests})")

def show_sample_images():
    """Show sample images from the dataset"""
    fig, axes = plt.subplots(2, 4, figsize=(12, 6))
    fig.suptitle('Sample Images from Dataset')
    
    for i, class_name in enumerate(['corn', 'pineapple']):
        class_path = os.path.join(DATASET_PATH, 'train', class_name)
        image_files = os.listdir(class_path)
        
        for j in range(4):
            img_path = os.path.join(class_path, image_files[j])
            img = tf.keras.preprocessing.image.load_img(img_path, target_size=IMG_SIZE)
            
            axes[i, j].imshow(img)
            axes[i, j].set_title(f'{class_name.capitalize()}')
            axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()

def main():
    """Main function"""
    print("🍍 Simple Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 50)
    
    # Set random seed
    tf.random.set_seed(42)
    np.random.seed(42)
    
    # Show sample images
    print("Showing sample images...")
    show_sample_images()
    
    # Create data generators
    train_gen, val_gen, test_gen = create_data_generators()
    
    # Build model
    model = build_cnn_model()
    
    # Train model
    history = train_model(model, train_gen, val_gen, epochs=8)
    
    # Plot training results
    plot_training_results(history)
    
    # Evaluate model
    test_acc, test_loss = evaluate_model(model, test_gen)
    
    # Test random predictions
    test_random_predictions(model, num_tests=10)
    
    # Save model
    model.save("simple_fruit_classifier.h5")
    print("Model saved as 'simple_fruit_classifier.h5'")
    
    print(f"\n🎉 Training completed! Final test accuracy: {test_acc:.4f}")

if __name__ == "__main__":
    main()
