import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# === Step 1: Load and preprocess image ===
def load_grayscale_image(img_path, target_size=(256, 256)):
    img = load_img(img_path, target_size=target_size, color_mode='grayscale')
    img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]
    img_array = np.expand_dims(img_array, axis=0)  # Shape: (1, H, W, 1)
    return img_array

# === Step 2: Define Edge Detection Kernel ===
def get_edge_filter(filter_type='horizontal'):
    if filter_type == 'horizontal':
        kernel = np.array([[-1, -1, -1],
                           [ 0,  0,  0],
                           [ 1,  1,  1]], dtype=np.float32)
    elif filter_type == 'vertical':
        kernel = np.array([[-1, 0, 1],
                           [-1, 0, 1],
                           [-1, 0, 1]], dtype=np.float32)
    elif filter_type == 'sobel_x':
        kernel = np.array([[-1, 0, 1],
                           [-2, 0, 2],
                           [-1, 0, 1]], dtype=np.float32)
    elif filter_type == 'sobel_y':
        kernel = np.array([[-1, -2, -1],
                           [ 0,  0,  0],
                           [ 1,  2,  1]], dtype=np.float32)
    else:
        raise ValueError("Unknown filter_type.")
    
    # add other filters here below with comments
    
        # elif filter_type == 'prewitt_x':
        #     kernel = np.array([[-1, 0, 1],
        #                       [-1, 0, 1],
        #                       [-1, 0, 1]], dtype=np.float32)
        # elif filter_type == 'prewitt_y':
        #     kernel = np.array([[-1, -1, -1],
        #                       [ 0,  0,  0],
        #                       [ 1,  1,  1]], dtype=np.float32)
        # elif filter_type == 'laplacian':
        #     kernel = np.array([[ 0,  1,  0],
        #                       [ 1, -4,  1],
        #                       [ 0,  1,  0]], dtype=np.float32)
        # elif filter_type == 'scharr_x':
        #     kernel = np.array([[ -3, 0,  3],
        #                       [-10, 0, 10],
        #                       [ -3, 0,  3]], dtype=np.float32)
        # elif filter_type == 'scharr_y':
        #     kernel = np.array([[-3, -10, -3],
        #                       [ 0,   0,  0],
        #                       [ 3,  10,  3]], dtype=np.float32)

    
    
                                
    return kernel.reshape((3, 3, 1, 1))

# === Step 3: Build Conv2D Layer with Fixed Weights ===
def apply_edge_filter(img_array, kernel):
    edge_layer = tf.keras.layers.Conv2D(filters=1,
                                        kernel_size=(3, 3),
                                        strides=(1, 1),
                                        padding='same',
                                        use_bias=False,
                                        trainable=False)
    
    edge_layer.build(input_shape=(None, img_array.shape[1], img_array.shape[2], 1))
    edge_layer.set_weights([kernel])
    
    output = edge_layer(img_array)
    return output

# === Step 4: Plot Original and Both Sobel Filters ===
def plot_sobel_results(original, sobel_x, sobel_y, sobel_combined):
    plt.figure(figsize=(16, 4))
    num_plots = 4

    plt.subplot(1, num_plots, 1)
    plt.title("Original")
    plt.imshow(original[0, :, :, 0], cmap='gray')
    plt.axis('off')

    plt.subplot(1, num_plots, 2)
    plt.title("Sobel X (Vertical Edges)")
    plt.imshow(sobel_x[0, :, :, 0], cmap='gray')
    plt.axis('off')

    plt.subplot(1, num_plots, 3)
    plt.title("Sobel Y (Horizontal Edges)")
    plt.imshow(sobel_y[0, :, :, 0], cmap='gray')
    plt.axis('off')

    plt.subplot(1, num_plots, 4)
    plt.title("Combined Sobel")
    plt.imshow(sobel_combined[0, :, :, 0], cmap='gray')
    plt.axis('off')

    plt.tight_layout()
    plt.show()

# === Run Everything ===
if __name__ == '__main__':
    image_path = r"C:\Users\<USER>\Pictures\Me\Me & Yves.jpg" # Replace with your actual image path
    img_array = load_grayscale_image(image_path)

    # Apply both Sobel filters
    sobel_x_kernel = get_edge_filter(filter_type='sobel_x')
    sobel_y_kernel = get_edge_filter(filter_type='sobel_y')

    sobel_x_result = apply_edge_filter(img_array, sobel_x_kernel)
    sobel_y_result = apply_edge_filter(img_array, sobel_y_kernel)

    # Compute combined Sobel magnitude
    sobel_combined = tf.sqrt(tf.square(sobel_x_result) + tf.square(sobel_y_result))

    # Display both Sobel filters comparison
    plot_sobel_results(img_array, sobel_x_result, sobel_y_result, sobel_combined)
