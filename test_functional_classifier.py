import tensorflow as tf
import matplotlib.pyplot as plt
import numpy as np
import os
import random
from fruit_classifier_functional import *

def explore_dataset(dataset_path=DATASET_PATH):
    """Explore the dataset and show statistics"""
    print("📊 Dataset Exploration")
    print("=" * 30)
    
    # Count images in each split
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(dataset_path, split)
        if os.path.exists(split_path):
            corn_count = len(os.listdir(os.path.join(split_path, 'corn')))
            pineapple_count = len(os.listdir(os.path.join(split_path, 'pineapple')))
            total = corn_count + pineapple_count
            print(f"{split.upper()} SET:")
            print(f"  Corn: {corn_count} images")
            print(f"  Pineapple: {pineapple_count} images")
            print(f"  Total: {total} images")
            print()

def show_sample_images(dataset_path=DATASET_PATH, num_samples=8):
    """Show sample images from the dataset"""
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    fig.suptitle('Sample Images from Dataset', fontsize=16)
    
    classes = ['corn', 'pineapple']
    
    for i, class_name in enumerate(classes):
        class_path = os.path.join(dataset_path, 'train', class_name)
        image_files = os.listdir(class_path)
        
        # Select random images
        selected_images = random.sample(image_files, 4)
        
        for j, img_file in enumerate(selected_images):
            img_path = os.path.join(class_path, img_file)
            img = tf.keras.preprocessing.image.load_img(img_path, target_size=IMG_SIZE)
            
            axes[i, j].imshow(img)
            axes[i, j].set_title(f'{class_name.capitalize()}')
            axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()

def quick_train_simple_cnn():
    """Quick training with simple CNN"""
    print("🚀 Quick Training - Simple CNN")
    print("=" * 35)
    
    # Setup data
    train_gen, val_gen, test_gen = setup_data_generators()
    
    # Build simple model
    model = build_simple_cnn_model()
    
    # Train for fewer epochs
    history = train_model(model, train_gen, val_gen, epochs=5)
    
    # Plot and evaluate
    plot_training_history(history)
    test_acc, test_loss = evaluate_model(model, test_gen)
    
    # Save model
    save_model(model, "simple_cnn_model.h5")
    
    return model, history

def full_train_improved_cnn():
    """Full training with improved CNN"""
    print("🎯 Full Training - Improved CNN")
    print("=" * 35)

    # Setup data
    train_gen, val_gen, test_gen = setup_data_generators()

    # Build improved CNN model
    model = build_improved_cnn_model()

    # Train
    history = train_model(model, train_gen, val_gen, epochs=15)

    # Plot and evaluate
    plot_training_history(history)
    test_acc, test_loss = evaluate_model(model, test_gen)

    # Save model
    save_model(model, "improved_cnn_model.h5")

    return model, history

def test_random_prediction(model, dataset_path=DATASET_PATH):
    """Test prediction on a random image"""
    print("🔍 Testing Random Prediction")
    print("=" * 30)
    
    # Get a random test image
    test_path = os.path.join(dataset_path, 'test')
    classes = ['corn', 'pineapple']
    
    # Select random class and image
    random_class = random.choice(classes)
    class_path = os.path.join(test_path, random_class)
    image_files = os.listdir(class_path)
    random_image = random.choice(image_files)
    image_path = os.path.join(class_path, random_image)
    
    print(f"Testing image: {random_image}")
    print(f"True class: {random_class}")
    
    # Make prediction
    predicted_class, confidence = predict_single_image(model, image_path)
    
    print(f"Predicted class: {predicted_class}")
    print(f"Confidence: {confidence:.2f}")
    
    # Check if prediction is correct
    correct = predicted_class.lower() == random_class.lower()
    print(f"Prediction correct: {correct}")
    
    return correct

def batch_test_predictions(model, num_tests=10, dataset_path=DATASET_PATH):
    """Test multiple random predictions"""
    print(f"🎯 Batch Testing - {num_tests} Random Predictions")
    print("=" * 45)
    
    correct_predictions = 0
    test_path = os.path.join(dataset_path, 'test')
    classes = ['corn', 'pineapple']
    
    for i in range(num_tests):
        # Select random class and image
        random_class = random.choice(classes)
        class_path = os.path.join(test_path, random_class)
        image_files = os.listdir(class_path)
        random_image = random.choice(image_files)
        image_path = os.path.join(class_path, random_image)
        
        # Load and preprocess image
        img = tf.keras.preprocessing.image.load_img(image_path, target_size=IMG_SIZE)
        img_array = tf.keras.preprocessing.image.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0) / 255.0
        
        # Make prediction
        prediction = model.predict(img_array, verbose=0)[0][0]
        predicted_class = CLASS_NAMES[1] if prediction > 0.5 else CLASS_NAMES[0]
        confidence = prediction if prediction > 0.5 else 1 - prediction
        
        # Check if correct
        correct = predicted_class.lower() == random_class.lower()
        if correct:
            correct_predictions += 1
            
        print(f"Test {i+1:2d}: True={random_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if correct else '✗'}")
    
    accuracy = correct_predictions / num_tests
    print(f"\nBatch Test Accuracy: {accuracy:.2f} ({correct_predictions}/{num_tests})")
    
    return accuracy

def interactive_demo():
    """Interactive demonstration"""
    model = None
    
    while True:
        print("\n" + "="*50)
        print("🍍 FUNCTIONAL FRUIT CLASSIFIER DEMO 🌽")
        print("="*50)
        print("1. Explore Dataset")
        print("2. Show Sample Images")
        print("3. Quick Train (Simple CNN - 5 epochs)")
        print("4. Full Train (Improved CNN - 15 epochs)")
        print("5. Test Single Random Prediction")
        print("6. Batch Test (10 predictions)")
        print("7. Load Saved Model")
        print("8. Exit")
        print("="*50)
        
        choice = input("Enter your choice (1-8): ").strip()
        
        if choice == '1':
            explore_dataset()
            
        elif choice == '2':
            show_sample_images()
            
        elif choice == '3':
            model, history = quick_train_simple_cnn()
            
        elif choice == '4':
            model, history = full_train_improved_cnn()
            
        elif choice == '5':
            if model is None:
                print("❌ No model loaded. Please train a model first (option 3 or 4).")
            else:
                test_random_prediction(model)
                
        elif choice == '6':
            if model is None:
                print("❌ No model loaded. Please train a model first (option 3 or 4).")
            else:
                batch_test_predictions(model)
                
        elif choice == '7':
            try:
                model_path = input("Enter model path (or press Enter for 'improved_cnn_model.h5'): ").strip()
                if not model_path:
                    model_path = "improved_cnn_model.h5"
                model = load_model(model_path)
                print("✅ Model loaded successfully!")
            except Exception as e:
                print(f"❌ Could not load model: {e}")
                
        elif choice == '8':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter a number between 1-8.")

if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)
    tf.random.set_seed(42)
    np.random.seed(42)
    
    # Run interactive demo
    interactive_demo()
