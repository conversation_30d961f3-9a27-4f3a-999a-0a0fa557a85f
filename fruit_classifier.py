import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.applications import MobileNetV2
from tensorflow.keras.layers import Dense, GlobalAveragePooling2D, Dropout
from tensorflow.keras.models import Model
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix

class FruitClassifier:
    def __init__(self, dataset_path="FruitDetection", img_size=(224, 224), batch_size=32):
        self.dataset_path = dataset_path
        self.img_size = img_size
        self.batch_size = batch_size
        self.model = None
        self.history = None
        self.class_names = ['corn', 'pineapple']
        
    def setup_data_generators(self):
        """Setup data generators with augmentation for training"""
        # Training data generator with augmentation
        train_datagen = ImageDataGenerator(
            rescale=1./255,
            rotation_range=20,
            width_shift_range=0.2,
            height_shift_range=0.2,
            horizontal_flip=True,
            zoom_range=0.2,
            shear_range=0.2,
            fill_mode='nearest'
        )
        
        # Validation and test data generators (no augmentation)
        val_test_datagen = ImageDataGenerator(rescale=1./255)
        
        # Create data generators
        self.train_generator = train_datagen.flow_from_directory(
            os.path.join(self.dataset_path, 'train'),
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            shuffle=True
        )
        
        self.val_generator = val_test_datagen.flow_from_directory(
            os.path.join(self.dataset_path, 'val'),
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            shuffle=False
        )
        
        self.test_generator = val_test_datagen.flow_from_directory(
            os.path.join(self.dataset_path, 'test'),
            target_size=self.img_size,
            batch_size=self.batch_size,
            class_mode='binary',
            shuffle=False
        )
        
        print(f"Training samples: {self.train_generator.samples}")
        print(f"Validation samples: {self.val_generator.samples}")
        print(f"Test samples: {self.test_generator.samples}")
        print(f"Class indices: {self.train_generator.class_indices}")
        
    def build_model(self, use_pretrained=True):
        """Build the CNN model"""
        if use_pretrained:
            # Use MobileNetV2 as base model (transfer learning)
            base_model = MobileNetV2(
                weights='imagenet',
                include_top=False,
                input_shape=(*self.img_size, 3)
            )
            
            # Freeze base model layers
            base_model.trainable = False
            
            # Add custom classification head
            x = base_model.output
            x = GlobalAveragePooling2D()(x)
            x = Dropout(0.2)(x)
            predictions = Dense(1, activation='sigmoid')(x)
            
            self.model = Model(inputs=base_model.input, outputs=predictions)
            
        else:
            # Build a simple CNN from scratch
            self.model = tf.keras.Sequential([
                tf.keras.layers.Conv2D(32, (3, 3), activation='relu', input_shape=(*self.img_size, 3)),
                tf.keras.layers.MaxPooling2D(2, 2),
                tf.keras.layers.Conv2D(64, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D(2, 2),
                tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D(2, 2),
                tf.keras.layers.Conv2D(128, (3, 3), activation='relu'),
                tf.keras.layers.MaxPooling2D(2, 2),
                tf.keras.layers.Flatten(),
                tf.keras.layers.Dropout(0.5),
                tf.keras.layers.Dense(512, activation='relu'),
                tf.keras.layers.Dense(1, activation='sigmoid')
            ])
        
        # Compile the model
        self.model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        print("Model built successfully!")
        print(f"Total parameters: {self.model.count_params():,}")
        
    def train_model(self, epochs=20):
        """Train the model"""
        # Setup callbacks
        callbacks = [
            EarlyStopping(
                monitor='val_loss',
                patience=5,
                restore_best_weights=True,
                verbose=1
            ),
            ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.2,
                patience=3,
                min_lr=0.0001,
                verbose=1
            )
        ]
        
        # Train the model
        self.history = self.model.fit(
            self.train_generator,
            epochs=epochs,
            validation_data=self.val_generator,
            callbacks=callbacks,
            verbose=1
        )
        
        print("Training completed!")
        
    def plot_training_history(self):
        """Plot training history"""
        if self.history is None:
            print("No training history available. Train the model first.")
            return
            
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
        
        # Plot accuracy
        ax1.plot(self.history.history['accuracy'], label='Training Accuracy')
        ax1.plot(self.history.history['val_accuracy'], label='Validation Accuracy')
        ax1.set_title('Model Accuracy')
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Accuracy')
        ax1.legend()
        ax1.grid(True)
        
        # Plot loss
        ax2.plot(self.history.history['loss'], label='Training Loss')
        ax2.plot(self.history.history['val_loss'], label='Validation Loss')
        ax2.set_title('Model Loss')
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss')
        ax2.legend()
        ax2.grid(True)
        
        plt.tight_layout()
        plt.show()
        
    def evaluate_model(self):
        """Evaluate model on test set"""
        if self.model is None:
            print("No model available. Build and train the model first.")
            return
            
        # Evaluate on test set
        test_loss, test_accuracy = self.model.evaluate(self.test_generator, verbose=1)
        print(f"\nTest Accuracy: {test_accuracy:.4f}")
        print(f"Test Loss: {test_loss:.4f}")
        
        # Get predictions
        predictions = self.model.predict(self.test_generator)
        predicted_classes = (predictions > 0.5).astype(int).flatten()
        true_classes = self.test_generator.classes
        
        # Classification report
        print("\nClassification Report:")
        print(classification_report(true_classes, predicted_classes, target_names=self.class_names))
        
        # Confusion matrix
        cm = confusion_matrix(true_classes, predicted_classes)
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.class_names, yticklabels=self.class_names)
        plt.title('Confusion Matrix')
        plt.ylabel('True Label')
        plt.xlabel('Predicted Label')
        plt.show()
        
        return test_accuracy, test_loss
        
    def predict_image(self, image_path):
        """Predict a single image"""
        if self.model is None:
            print("No model available. Build and train the model first.")
            return
            
        # Load and preprocess image
        img = tf.keras.preprocessing.image.load_img(image_path, target_size=self.img_size)
        img_array = tf.keras.preprocessing.image.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0) / 255.0
        
        # Make prediction
        prediction = self.model.predict(img_array)[0][0]
        predicted_class = self.class_names[1] if prediction > 0.5 else self.class_names[0]
        confidence = prediction if prediction > 0.5 else 1 - prediction
        
        # Display result
        plt.figure(figsize=(8, 6))
        plt.imshow(img)
        plt.title(f'Prediction: {predicted_class} (Confidence: {confidence:.2f})')
        plt.axis('off')
        plt.show()
        
        return predicted_class, confidence
        
    def save_model(self, filepath="fruit_classifier_model.h5"):
        """Save the trained model"""
        if self.model is None:
            print("No model to save. Build and train the model first.")
            return
            
        self.model.save(filepath)
        print(f"Model saved to {filepath}")
        
    def load_model(self, filepath="fruit_classifier_model.h5"):
        """Load a saved model"""
        self.model = tf.keras.models.load_model(filepath)
        print(f"Model loaded from {filepath}")

def main():
    """Main function to run the fruit classifier"""
    print("🍍 Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 50)
    
    # Initialize classifier
    classifier = FruitClassifier()
    
    # Setup data
    print("Setting up data generators...")
    classifier.setup_data_generators()
    
    # Build model
    print("\nBuilding model...")
    classifier.build_model(use_pretrained=True)  # Set to False for simple CNN
    
    # Train model
    print("\nStarting training...")
    classifier.train_model(epochs=15)
    
    # Plot training history
    print("\nPlotting training history...")
    classifier.plot_training_history()
    
    # Evaluate model
    print("\nEvaluating model...")
    classifier.evaluate_model()
    
    # Save model
    classifier.save_model()
    
    print("\n🎉 Training completed successfully!")

if __name__ == "__main__":
    main()
