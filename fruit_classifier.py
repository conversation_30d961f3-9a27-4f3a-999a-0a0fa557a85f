import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 150
CLASS_NAMES = ['corn', 'pineapple']

print("🔧 Using TensorFlow for image classification")

def load_images_from_folder(folder_path, label):
    """Load images from a folder and assign labels"""
    images = []
    labels = []

    image_files = os.listdir(folder_path)
    print(f"Loading {len(image_files)} images from {folder_path}...")

    for i, filename in enumerate(image_files):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                img = load_img(img_path, target_size=(IMG_SIZE, IMG_SIZE))
                img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]

                images.append(img_array)
                labels.append(label)

                if (i + 1) % 100 == 0:
                    print(f"  Loaded {i + 1}/{len(image_files)} images")

            except Exception as e:
                print(f"Error loading {filename}: {e}")

    return np.array(images), np.array(labels)

def load_dataset():
    """Load the complete dataset"""
    print("🍍 Loading fruit classification dataset...")

    # Initialize counters
    train_count = 0
    test_count = 0

    # Count training and validation images
    for split in ['train', 'val']:
        for class_name in ['corn', 'pineapple']:
            path = os.path.join(DATASET_PATH, split, class_name)
            train_count += len([f for f in os.listdir(path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])

    # Count test images
    for class_name in ['corn', 'pineapple']:
        path = os.path.join(DATASET_PATH, 'test', class_name)
        test_count += len([f for f in os.listdir(path) if f.lower().endswith(('.png', '.jpg', '.jpeg'))])

    print(f"Training set: {train_count} images")
    print(f"Test set: {test_count} images")

    return train_count, 0, test_count, 0

def prepare_data(X_train, X_test):
    """Prepare data for TensorFlow (already normalized)"""
    print("Data already normalized during loading...")
    return X_train, X_test

def create_cnn_model():
    """Create a simple CNN model using TensorFlow functional API"""
    print("🏗️ Creating CNN model...")

    try:
        # Use functional API to avoid compatibility issues
        inputs = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))

        # First convolutional block
        x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(inputs)
        x = tf.keras.layers.MaxPooling2D((2, 2))(x)

        # Second convolutional block
        x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)
        x = tf.keras.layers.MaxPooling2D((2, 2))(x)

        # Third convolutional block
        x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
        x = tf.keras.layers.MaxPooling2D((2, 2))(x)

        # Flatten and dense layers
        x = tf.keras.layers.Flatten()(x)
        x = tf.keras.layers.Dense(128, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.5)(x)
        outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)

        model = tf.keras.Model(inputs=inputs, outputs=outputs)

        # Compile model
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        print("✅ Model created!")
        return model

    except Exception as e:
        print(f"❌ Error creating TensorFlow model: {e}")
        print("🔄 Falling back to simple dense network...")

        # Fallback: simple dense network
        inputs = tf.keras.Input(shape=(IMG_SIZE * IMG_SIZE * 3,))
        x = tf.keras.layers.Dense(512, activation='relu')(inputs)
        x = tf.keras.layers.Dropout(0.5)(x)
        x = tf.keras.layers.Dense(256, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.5)(x)
        outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)

        model = tf.keras.Model(inputs=inputs, outputs=outputs)
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        print("✅ Fallback model created!")
        return model

def train_classifier(X_train, y_train):
    """Train CNN classifier"""
    print("🚀 Training CNN classifier...")

    # Create model
    model = create_cnn_model()

    # Check if we need to flatten data for dense network
    if len(model.input.shape) == 2:  # Dense network expects flattened input
        print("🔄 Flattening data for dense network...")
        X_train_processed = X_train.reshape(X_train.shape[0], -1)
    else:  # CNN expects image format
        X_train_processed = X_train

    # Train model
    history = model.fit(
        X_train_processed, y_train,
        batch_size=32,
        epochs=8,
        validation_split=0.2,
        verbose=1
    )

    print("✅ Training completed!")
    return model, history

def evaluate_classifier(model, X_test, y_test):
    """Evaluate the classifier"""
    print("📊 Evaluating classifier...")

    # Check if we need to flatten data
    if len(model.input.shape) == 2:  # Dense network
        X_test_processed = X_test.reshape(X_test.shape[0], -1)
    else:  # CNN
        X_test_processed = X_test

    # Get predictions
    y_pred_proba = model.predict(X_test_processed)
    y_pred = (y_pred_proba > 0.5).astype(int).flatten()

    # Calculate accuracy
    accuracy = tf.keras.metrics.binary_accuracy(y_test, y_pred)
    accuracy = tf.reduce_mean(accuracy)

    print(f"Test Accuracy: {accuracy:.4f}")
    print("\nClassification Report:")
    print(tf.keras.metrics.classification_report(y_test, y_pred, target_names=CLASS_NAMES))

    # Confusion matrix
    cm = tf.math.confusion_matrix(y_test, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
               xticklabels=CLASS_NAMES, yticklabels=CLASS_NAMES)
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()

    return accuracy

def show_sample_images(X_train, y_train):
    """Show sample images"""
    print("📸 Showing sample images...")

    plt.figure(figsize=(12, 6))

    # Show 4 corn images
    corn_indices = tf.where(y_train == 0)[:4]
    for i, idx in enumerate(corn_indices):
        plt.subplot(2, 4, i + 1)
        img = X_train[idx[0]]
        plt.imshow(img)
        plt.title('Corn')
        plt.axis('off')

    # Show 4 pineapple images
    pineapple_indices = tf.where(y_train == 1)[:4]
    for i, idx in enumerate(pineapple_indices):
        plt.subplot(2, 4, i + 5)
        img = X_train[idx[0]]
        plt.imshow(img)
        plt.title('Pineapple')
        plt.axis('off')

    plt.tight_layout()
    plt.show()

def test_random_predictions(model, X_test, y_test, num_tests=10):
    """Test random predictions"""
    print(f"🔍 Testing {num_tests} random predictions...")

    indices = tf.random.shuffle(tf.range(len(X_test)))[:num_tests]
    correct = 0

    for i, idx in enumerate(indices):
        # Prepare input
        if len(model.input.shape) == 2:  # Dense network
            input_data = tf.reshape(X_test[idx], (1, -1))
        else:  # CNN
            input_data = tf.expand_dims(X_test[idx], 0)

        # Get prediction probability
        prediction_proba = model.predict(input_data, verbose=0)[0][0]
        prediction = tf.cast(prediction_proba > 0.5, tf.int32)
        predicted_class = CLASS_NAMES[prediction]
        true_class = CLASS_NAMES[y_test[idx]]

        # Get confidence
        confidence = prediction_proba if prediction_proba > 0.5 else 1 - prediction_proba

        is_correct = predicted_class == true_class
        if is_correct:
            correct += 1

        print(f"Test {i+1:2d}: True={true_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")

    accuracy = correct / num_tests
    print(f"\nRandom test accuracy: {accuracy:.2f} ({correct}/{num_tests})")
    return accuracy

def show_prediction_samples(model, X_test, y_test, num_samples=6):
    """Show prediction samples with images"""
    print(f"🖼️ Showing {num_samples} prediction samples...")

    indices = tf.random.shuffle(tf.range(len(X_test)))[:num_samples]

    plt.figure(figsize=(15, 10))
    for i, idx in enumerate(indices):
        plt.subplot(2, 3, i + 1)

        # Show image
        img = X_test[idx]
        plt.imshow(img)

        # Prepare input for prediction
        if len(model.input.shape) == 2:  # Dense network
            input_data = tf.reshape(X_test[idx], (1, -1))
        else:  # CNN
            input_data = tf.expand_dims(X_test[idx], 0)

        # Make prediction
        prediction_proba = model.predict(input_data, verbose=0)[0][0]
        prediction = tf.cast(prediction_proba > 0.5, tf.int32)
        predicted_class = CLASS_NAMES[prediction]
        true_class = CLASS_NAMES[y_test[idx]]

        confidence = prediction_proba if prediction_proba > 0.5 else 1 - prediction_proba

        # Color: green if correct, red if wrong
        color = 'green' if predicted_class == true_class else 'red'

        plt.title(f"True: {true_class}\nPred: {predicted_class}\nConf: {confidence:.2f}",
                 color=color, fontsize=10)
        plt.axis('off')

    plt.tight_layout()
    plt.show()

def main():
    """Main function - train and test fruit classifier"""
    print("🍍 TensorFlow Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 55)

    # Set random seeds
    tf.random.set_seed(42)
    np.random.seed(42)

    # Load dataset
    X_train, y_train, X_test, y_test = load_dataset()

    # Show sample images
    show_sample_images(X_train, y_train)

    # Prepare data (already normalized)
    X_train_prepared, X_test_prepared = prepare_data(X_train, X_test)

    # Train classifier
    model, history = train_classifier(X_train_prepared, y_train)

    # Plot training history
    plt.figure(figsize=(12, 4))
    plt.subplot(1, 2, 1)
    plt.plot(history.history['accuracy'], label='Training Accuracy')
    plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
    plt.title('Model Accuracy')
    plt.xlabel('Epoch')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)

    plt.subplot(1, 2, 2)
    plt.plot(history.history['loss'], label='Training Loss')
    plt.plot(history.history['val_loss'], label='Validation Loss')
    plt.title('Model Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.show()

    # Evaluate classifier
    accuracy = evaluate_classifier(model, X_test_prepared, y_test)

    # Test random predictions
    test_random_predictions(model, X_test_prepared, y_test, num_tests=15)

    # Show prediction samples with images
    show_prediction_samples(model, X_test_prepared, y_test, num_samples=6)

    # Save model
    model.save("fruit_classifier_cnn.h5")
    print("Model saved as 'fruit_classifier_cnn.h5'")

    print(f"\n🎉 TensorFlow fruit classifier completed! Final accuracy: {accuracy:.4f}")

if __name__ == "__main__":
    main()
