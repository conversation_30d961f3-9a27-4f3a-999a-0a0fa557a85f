import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 150
CLASS_NAMES = ['corn', 'pineapple']

print("🔧 Using TensorFlow for image classification")

def load_images_from_folder(folder_path, label):
    """Load images from a folder and assign labels"""
    images = []
    labels = []

    image_files = os.listdir(folder_path)
    print(f"Loading {len(image_files)} images from {folder_path}...")

    for i, filename in enumerate(image_files):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                img = load_img(img_path, target_size=(IMG_SIZE, IMG_SIZE))
                img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]

                images.append(img_array)
                labels.append(label)

                if (i + 1) % 100 == 0:
                    print(f"  Loaded {i + 1}/{len(image_files)} images")

            except Exception as e:
                print(f"Error loading {filename}: {e}")

    return np.array(images), np.array(labels)

def load_dataset():
    """Load the complete dataset"""
    print("🍍 Loading fruit classification dataset...")

    # Load training data (train + val)
    train_corn_images, train_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'corn'), 0
    )
    train_pineapple_images, train_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1
    )

    val_corn_images, val_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'corn'), 0
    )
    val_pineapple_images, val_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1
    )

    # Load test data
    test_corn_images, test_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'corn'), 0
    )
    test_pineapple_images, test_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1
    )

    # Combine training and validation data
    X_train = np.concatenate([train_corn_images, train_pineapple_images,
                             val_corn_images, val_pineapple_images])
    y_train = np.concatenate([train_corn_labels, train_pineapple_labels,
                             val_corn_labels, val_pineapple_labels])

    X_test = np.concatenate([test_corn_images, test_pineapple_images])
    y_test = np.concatenate([test_corn_labels, test_pineapple_labels])

    # Shuffle training data
    train_indices = np.random.permutation(len(X_train))
    X_train = X_train[train_indices]
    y_train = y_train[train_indices]

    print(f"Training set: {len(X_train)} images")
    print(f"Test set: {len(X_test)} images")

    return X_train, y_train, X_test, y_test

def prepare_data(X_train, X_test):
    """Prepare data for TensorFlow (already normalized)"""
    print("Data already normalized during loading...")
    return X_train, X_test

def create_cnn_model():
    """Create a simple CNN model using TensorFlow functional API"""
    print("🏗️ Creating CNN model...")

    try:
        # Use functional API to avoid compatibility issues
        inputs = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))

        # First convolutional block
        x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(inputs)
        x = tf.keras.layers.MaxPooling2D((2, 2))(x)

        # Second convolutional block
        x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)
        x = tf.keras.layers.MaxPooling2D((2, 2))(x)

        # Third convolutional block
        x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
        x = tf.keras.layers.MaxPooling2D((2, 2))(x)

        # Flatten and dense layers
        x = tf.keras.layers.Flatten()(x)
        x = tf.keras.layers.Dense(128, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.5)(x)
        outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)

        model = tf.keras.Model(inputs=inputs, outputs=outputs)

        # Compile model
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        print("✅ Model created!")
        return model

    except Exception as e:
        print(f"❌ Error creating TensorFlow model: {e}")
        print("🔄 Falling back to simple dense network...")

        # Fallback: simple dense network
        inputs = tf.keras.Input(shape=(IMG_SIZE * IMG_SIZE * 3,))
        x = tf.keras.layers.Dense(512, activation='relu')(inputs)
        x = tf.keras.layers.Dropout(0.5)(x)
        x = tf.keras.layers.Dense(256, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.5)(x)
        outputs = tf.keras.layers.Dense(1, activation='sigmoid')(x)

        model = tf.keras.Model(inputs=inputs, outputs=outputs)
        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        print("✅ Fallback model created!")
        return model

def train_classifier(X_train, y_train):
    """Train CNN classifier"""
    print("🚀 Training CNN classifier...")

    # Create model
    model = create_cnn_model()

    # Check if we need to flatten data for dense network
    if len(model.input.shape) == 2:  # Dense network expects flattened input
        print("🔄 Flattening data for dense network...")
        X_train_processed = X_train.reshape(X_train.shape[0], -1)
    else:  # CNN expects image format
        X_train_processed = X_train

    # Convert to numpy arrays if needed
    X_train_processed = np.array(X_train_processed)
    y_train = np.array(y_train)

    print(f"X_train shape: {X_train_processed.shape}")
    print(f"y_train shape: {y_train.shape}")
    print(f"Model input shape: {model.input.shape}")

    # Train model
    history = model.fit(
        X_train_processed, y_train,
        batch_size=32,
        epochs=8,
        validation_split=0.2,
        verbose=1
    )

    print("✅ Training completed!")
    return model, history

def evaluate_classifier(model, X_test, y_test):
    """Evaluate the classifier"""
    print("📊 Evaluating classifier...")

    # Check if we need to flatten data
    if len(model.input.shape) == 2:  # Dense network
        X_test_processed = X_test.reshape(X_test.shape[0], -1)
    else:  # CNN
        X_test_processed = X_test

    # Get predictions
    y_pred_proba = model.predict(X_test_processed)
    y_pred = (y_pred_proba > 0.5).astype(int).flatten()

    # Calculate accuracy
    accuracy = tf.keras.metrics.binary_accuracy(y_test, y_pred)
    accuracy = tf.reduce_mean(accuracy)

    print(f"Test Accuracy: {accuracy:.4f}")
    print("\nClassification Report:")
    print(tf.keras.metrics.classification_report(y_test, y_pred, target_names=CLASS_NAMES))

    # Confusion matrix
    cm = tf.math.confusion_matrix(y_test, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
               xticklabels=CLASS_NAMES, yticklabels=CLASS_NAMES)
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()

    return accuracy



def test_random_predictions(model, X_test, y_test, num_tests=10):
    """Test random predictions"""
    print(f"🔍 Testing {num_tests} random predictions...")

    indices = tf.random.shuffle(tf.range(len(X_test)))[:num_tests]
    correct = 0

    for i, idx in enumerate(indices):
        # Prepare input
        if len(model.input.shape) == 2:  # Dense network
            input_data = tf.reshape(X_test[idx], (1, -1))
        else:  # CNN
            input_data = tf.expand_dims(X_test[idx], 0)

        # Get prediction probability
        prediction_proba = model.predict(input_data, verbose=0)[0][0]
        prediction = tf.cast(prediction_proba > 0.5, tf.int32)
        predicted_class = CLASS_NAMES[prediction]
        true_class = CLASS_NAMES[y_test[idx]]

        # Get confidence
        confidence = prediction_proba if prediction_proba > 0.5 else 1 - prediction_proba

        is_correct = predicted_class == true_class
        if is_correct:
            correct += 1

        print(f"Test {i+1:2d}: True={true_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")

    accuracy = correct / num_tests
    print(f"\nRandom test accuracy: {accuracy:.2f} ({correct}/{num_tests})")
    return accuracy



def main():
    """Main function - train and test fruit classifier"""
    print("🍍 TensorFlow Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 55)

    # Set random seeds
    tf.random.set_seed(42)
    np.random.seed(42)

    # Load dataset
    X_train, y_train, X_test, y_test = load_dataset()

    # Skip sample images display

    # Prepare data (already normalized)
    X_train_prepared, X_test_prepared = prepare_data(X_train, X_test)

    # Train classifier
    model, history = train_classifier(X_train_prepared, y_train)

    # Training completed - history available if needed

    # Evaluate classifier
    accuracy = evaluate_classifier(model, X_test_prepared, y_test)

    # Test random predictions
    test_random_predictions(model, X_test_prepared, y_test, num_tests=10)

    # Save model
    model.save("fruit_classifier_cnn.h5")
    print("Model saved as 'fruit_classifier_cnn.h5'")

    print(f"\n🎉 TensorFlow fruit classifier completed! Final accuracy: {accuracy:.4f}")

if __name__ == "__main__":
    main()
