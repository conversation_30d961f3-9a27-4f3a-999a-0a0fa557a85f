import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import seaborn as sns

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 64
CLASS_NAMES = ['corn', 'pineapple']

def load_images_from_folder(folder_path, label):
    """Load images from a folder and assign labels"""
    images = []
    labels = []

    image_files = os.listdir(folder_path)
    print(f"Loading {len(image_files)} images from {folder_path}...")

    for i, filename in enumerate(image_files):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                img = Image.open(img_path)
                img = img.convert('RGB')
                img = img.resize((IMG_SIZE, IMG_SIZE))
                img_array = np.array(img).flatten()  # Flatten for sklearn

                images.append(img_array)
                labels.append(label)

                if (i + 1) % 100 == 0:
                    print(f"  Loaded {i + 1}/{len(image_files)} images")

            except Exception as e:
                print(f"Error loading {filename}: {e}")

    return np.array(images), np.array(labels)

def load_dataset():
    """Load the complete dataset"""
    print("🍍 Loading fruit classification dataset...")

    # Load all data
    train_corn_images, train_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'corn'), 0
    )
    train_pineapple_images, train_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1
    )

    val_corn_images, val_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'corn'), 0
    )
    val_pineapple_images, val_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1
    )

    test_corn_images, test_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'corn'), 0
    )
    test_pineapple_images, test_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1
    )

    # Combine training and validation data
    X_train = np.concatenate([train_corn_images, train_pineapple_images,
                             val_corn_images, val_pineapple_images])
    y_train = np.concatenate([train_corn_labels, train_pineapple_labels,
                             val_corn_labels, val_pineapple_labels])

    X_test = np.concatenate([test_corn_images, test_pineapple_images])
    y_test = np.concatenate([test_corn_labels, test_pineapple_labels])

    # Shuffle training data
    train_indices = np.random.permutation(len(X_train))
    X_train = X_train[train_indices]
    y_train = y_train[train_indices]

    print(f"Training set: {len(X_train)} images")
    print(f"Test set: {len(X_test)} images")

    return X_train, y_train, X_test, y_test

def normalize_data(X_train, X_test):
    """Normalize the data"""
    print("Normalizing data...")
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    return X_train_scaled, X_test_scaled

def train_classifier(X_train, y_train):
    """Train Random Forest classifier"""
    print("🚀 Training Random Forest classifier...")

    model = RandomForestClassifier(
        n_estimators=100,
        max_depth=20,
        random_state=42,
        n_jobs=-1,
        verbose=1
    )

    model.fit(X_train, y_train)
    print("✅ Training completed!")
    return model

def evaluate_classifier(model, X_test, y_test):
    """Evaluate the classifier"""
    print("📊 Evaluating classifier...")

    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    print(f"Test Accuracy: {accuracy:.4f}")
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred, target_names=CLASS_NAMES))

    # Confusion matrix
    cm = confusion_matrix(y_test, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
               xticklabels=CLASS_NAMES, yticklabels=CLASS_NAMES)
    plt.title('Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()

    return accuracy

def show_sample_images(X_train, y_train):
    """Show sample images"""
    print("📸 Showing sample images...")

    plt.figure(figsize=(12, 6))

    # Show 4 corn images
    corn_indices = np.where(y_train == 0)[0][:4]
    for i, idx in enumerate(corn_indices):
        plt.subplot(2, 4, i + 1)
        img = X_train[idx].reshape(IMG_SIZE, IMG_SIZE, 3).astype(np.uint8)
        plt.imshow(img)
        plt.title('Corn')
        plt.axis('off')

    # Show 4 pineapple images
    pineapple_indices = np.where(y_train == 1)[0][:4]
    for i, idx in enumerate(pineapple_indices):
        plt.subplot(2, 4, i + 5)
        img = X_train[idx].reshape(IMG_SIZE, IMG_SIZE, 3).astype(np.uint8)
        plt.imshow(img)
        plt.title('Pineapple')
        plt.axis('off')

    plt.tight_layout()
    plt.show()

def test_random_predictions(model, X_test, y_test, num_tests=10):
    """Test random predictions"""
    print(f"🔍 Testing {num_tests} random predictions...")

    indices = np.random.choice(len(X_test), num_tests, replace=False)
    correct = 0

    for i, idx in enumerate(indices):
        prediction = model.predict([X_test[idx]])[0]
        predicted_class = CLASS_NAMES[prediction]
        true_class = CLASS_NAMES[y_test[idx]]

        # Get confidence
        proba = model.predict_proba([X_test[idx]])[0]
        confidence = np.max(proba)

        is_correct = predicted_class == true_class
        if is_correct:
            correct += 1

        print(f"Test {i+1:2d}: True={true_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")

    accuracy = correct / num_tests
    print(f"\nRandom test accuracy: {accuracy:.2f} ({correct}/{num_tests})")
    return accuracy

def show_prediction_samples(model, X_test, y_test, num_samples=6):
    """Show prediction samples with images"""
    print(f"🖼️ Showing {num_samples} prediction samples...")

    indices = np.random.choice(len(X_test), num_samples, replace=False)

    plt.figure(figsize=(15, 10))
    for i, idx in enumerate(indices):
        plt.subplot(2, 3, i + 1)

        # Reshape and show image
        img = X_test[idx].reshape(IMG_SIZE, IMG_SIZE, 3).astype(np.uint8)
        plt.imshow(img)

        # Make prediction
        prediction = model.predict([X_test[idx]])[0]
        predicted_class = CLASS_NAMES[prediction]
        true_class = CLASS_NAMES[y_test[idx]]

        proba = model.predict_proba([X_test[idx]])[0]
        confidence = np.max(proba)

        # Color: green if correct, red if wrong
        color = 'green' if predicted_class == true_class else 'red'

        plt.title(f"True: {true_class}\nPred: {predicted_class}\nConf: {confidence:.2f}",
                 color=color, fontsize=10)
        plt.axis('off')

    plt.tight_layout()
    plt.show()

def main():
    """Main function - train and test fruit classifier"""
    print("🍍 Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 50)

    # Set random seed
    np.random.seed(42)

    # Load dataset
    X_train, y_train, X_test, y_test = load_dataset()

    # Show sample images
    show_sample_images(X_train, y_train)

    # Normalize data
    X_train_scaled, X_test_scaled = normalize_data(X_train, X_test)

    # Train classifier
    model = train_classifier(X_train_scaled, y_train)

    # Evaluate classifier
    accuracy = evaluate_classifier(model, X_test_scaled, y_test)

    # Test random predictions
    test_random_predictions(model, X_test_scaled, y_test, num_tests=15)

    # Show prediction samples with images
    show_prediction_samples(model, X_test_scaled, y_test, num_samples=6)

    print(f"\n🎉 Fruit classifier completed! Final accuracy: {accuracy:.4f}")

if __name__ == "__main__":
    main()
