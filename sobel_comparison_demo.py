import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing.image import load_img, img_to_array

def load_grayscale_image(img_path, target_size=(256, 256)):
    """Load and preprocess image for edge detection"""
    img = load_img(img_path, target_size=target_size, color_mode='grayscale')
    img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]
    img_array = np.expand_dims(img_array, axis=0)  # Shape: (1, H, W, 1)
    return img_array

def get_sobel_kernels():
    """Get both Sobel X and Sobel Y kernels"""
    sobel_x = np.array([[-1, 0, 1],
                        [-2, 0, 2],
                        [-1, 0, 1]], dtype=np.float32)
    
    sobel_y = np.array([[-1, -2, -1],
                        [ 0,  0,  0],
                        [ 1,  2,  1]], dtype=np.float32)
    
    # Reshape for TensorFlow Conv2D: (height, width, input_channels, output_channels)
    sobel_x = sobel_x.reshape((3, 3, 1, 1))
    sobel_y = sobel_y.reshape((3, 3, 1, 1))
    
    return sobel_x, sobel_y

def apply_sobel_filters(img_array):
    """Apply both Sobel X and Y filters to the image"""
    sobel_x_kernel, sobel_y_kernel = get_sobel_kernels()
    
    # Create Conv2D layers for both filters
    sobel_x_layer = tf.keras.layers.Conv2D(filters=1, kernel_size=(3, 3), 
                                           strides=(1, 1), padding='same', 
                                           use_bias=False, trainable=False)
    
    sobel_y_layer = tf.keras.layers.Conv2D(filters=1, kernel_size=(3, 3), 
                                           strides=(1, 1), padding='same', 
                                           use_bias=False, trainable=False)
    
    # Build layers and set weights
    sobel_x_layer.build(input_shape=(None, img_array.shape[1], img_array.shape[2], 1))
    sobel_y_layer.build(input_shape=(None, img_array.shape[1], img_array.shape[2], 1))
    
    sobel_x_layer.set_weights([sobel_x_kernel])
    sobel_y_layer.set_weights([sobel_y_kernel])
    
    # Apply filters
    sobel_x_result = sobel_x_layer(img_array)
    sobel_y_result = sobel_y_layer(img_array)
    
    # Compute combined magnitude
    sobel_magnitude = tf.sqrt(tf.square(sobel_x_result) + tf.square(sobel_y_result))
    
    return sobel_x_result, sobel_y_result, sobel_magnitude

def plot_sobel_comparison(original, sobel_x, sobel_y, sobel_magnitude):
    """Plot original image and all three Sobel results"""
    plt.figure(figsize=(16, 4))
    
    # Original image
    plt.subplot(1, 4, 1)
    plt.title("Original Image", fontsize=12)
    plt.imshow(original[0, :, :, 0], cmap='gray')
    plt.axis('off')
    
    # Sobel X (detects vertical edges)
    plt.subplot(1, 4, 2)
    plt.title("Sobel X\n(Vertical Edges)", fontsize=12)
    plt.imshow(sobel_x[0, :, :, 0], cmap='gray')
    plt.axis('off')
    
    # Sobel Y (detects horizontal edges)
    plt.subplot(1, 4, 3)
    plt.title("Sobel Y\n(Horizontal Edges)", fontsize=12)
    plt.imshow(sobel_y[0, :, :, 0], cmap='gray')
    plt.axis('off')
    
    # Combined magnitude
    plt.subplot(1, 4, 4)
    plt.title("Combined Sobel\n(Magnitude)", fontsize=12)
    plt.imshow(sobel_magnitude[0, :, :, 0], cmap='gray')
    plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def main():
    """Main function to demonstrate simultaneous Sobel computation"""
    # Load image
    image_path = r"C:\Users\<USER>\Pictures\CARD.png"  # Replace with your image path
    print("Loading image...")
    img_array = load_grayscale_image(image_path)
    
    # Apply both Sobel filters simultaneously
    print("Computing Sobel X and Y filters...")
    sobel_x, sobel_y, sobel_magnitude = apply_sobel_filters(img_array)
    
    # Display results
    print("Displaying results...")
    plot_sobel_comparison(img_array, sobel_x, sobel_y, sobel_magnitude)
    
    # Print some statistics
    print(f"\nImage shape: {img_array.shape}")
    print(f"Sobel X range: [{tf.reduce_min(sobel_x):.3f}, {tf.reduce_max(sobel_x):.3f}]")
    print(f"Sobel Y range: [{tf.reduce_min(sobel_y):.3f}, {tf.reduce_max(sobel_y):.3f}]")
    print(f"Combined magnitude range: [{tf.reduce_min(sobel_magnitude):.3f}, {tf.reduce_max(sobel_magnitude):.3f}]")

if __name__ == "__main__":
    main()
