import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from fruit_classifier_functional import *

def quick_demo():
    """Quick demonstration of the fruit classifier"""
    print("🍍 Quick Fruit Classifier Demo 🌽")
    print("=" * 40)
    
    # Set random seeds
    tf.random.set_seed(42)
    np.random.seed(42)
    
    # Setup data generators
    print("Setting up data...")
    train_gen, val_gen, test_gen = setup_data_generators()
    
    # Build simple model for quick demo
    print("\nBuilding simple CNN model...")
    model = build_simple_cnn_model()
    
    # Train for just a few epochs for demo
    print("\nTraining model (quick demo - 3 epochs)...")
    history = train_model(model, train_gen, val_gen, epochs=3)
    
    # Plot training progress
    print("\nPlotting training history...")
    plot_training_history(history)
    
    # Quick evaluation
    print("\nEvaluating model...")
    test_acc, test_loss = evaluate_model(model, test_gen)
    
    # Save the model
    save_model(model, "quick_demo_model.h5")
    
    print(f"\n🎉 Quick demo completed! Test accuracy: {test_acc:.4f}")
    
    return model

def test_predictions(model, num_tests=5):
    """Test some predictions"""
    print(f"\n🔍 Testing {num_tests} random predictions...")
    
    test_path = os.path.join(DATASET_PATH, 'test')
    classes = ['corn', 'pineapple']
    correct = 0
    
    for i in range(num_tests):
        # Get random image
        random_class = np.random.choice(classes)
        class_path = os.path.join(test_path, random_class)
        image_files = os.listdir(class_path)
        random_image = np.random.choice(image_files)
        image_path = os.path.join(class_path, random_image)
        
        # Load and predict
        img = tf.keras.preprocessing.image.load_img(image_path, target_size=IMG_SIZE)
        img_array = tf.keras.preprocessing.image.img_to_array(img)
        img_array = np.expand_dims(img_array, axis=0) / 255.0
        
        prediction = model.predict(img_array, verbose=0)[0][0]
        predicted_class = CLASS_NAMES[1] if prediction > 0.5 else CLASS_NAMES[0]
        confidence = prediction if prediction > 0.5 else 1 - prediction
        
        is_correct = predicted_class == random_class
        if is_correct:
            correct += 1
            
        print(f"Test {i+1}: True={random_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")
    
    accuracy = correct / num_tests
    print(f"\nQuick test accuracy: {accuracy:.2f} ({correct}/{num_tests})")

def show_dataset_info():
    """Show basic dataset information"""
    print("📊 Dataset Information")
    print("=" * 25)
    
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(DATASET_PATH, split)
        if os.path.exists(split_path):
            corn_count = len(os.listdir(os.path.join(split_path, 'corn')))
            pineapple_count = len(os.listdir(os.path.join(split_path, 'pineapple')))
            total = corn_count + pineapple_count
            print(f"{split.upper()}: {corn_count} corn + {pineapple_count} pineapple = {total} total")

def main():
    """Main demo function"""
    print("Starting fruit classifier demonstration...")
    
    # Show dataset info
    show_dataset_info()
    
    # Run quick demo
    model = quick_demo()
    
    # Test some predictions
    test_predictions(model, num_tests=10)
    
    print("\n✅ Demo completed successfully!")

if __name__ == "__main__":
    main()
