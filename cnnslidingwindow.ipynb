import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# Configuration (should match your main notebook)
IMG_SIZE = 150
CLASS_NAMES = ['corn', 'pineapple']
DATASET_PATH = "FruitDetection"

print("🚀 Brute-Force Object Detection with Sliding Window + CNN")
print(f"TensorFlow version: {tf.__version__}")
print(f"Image size: {IMG_SIZE}x{IMG_SIZE}")
print(f"Classes: {CLASS_NAMES}")

# Load your trained model
# Option 1: Load from saved file
try:
    model = tf.keras.models.load_model("fruit_classifier_with_localization.h5")
    print("✅ Model loaded from file: fruit_classifier_with_localization.h5")
except:
    print("❌ Could not load model from file")
    print("💡 Please ensure you have trained and saved your model first")
    print("💡 Or run the training notebook and then come back to this one")
    model = None

# Option 2: If you have the model in memory from another notebook
# model = your_trained_model  # Uncomment and set this if you have the model variable

if model is not None:
    print("\n📋 Model Summary:")
    model.summary()
else:
    print("\n⚠️ No model available. Please load or train a model first.")

def sliding_window_detection_with_localization(model, image, window_size=250, stride=125, threshold=0.7):
    """Perform sliding window object detection using our current localization model"""
    print(f"🔍 Performing sliding window detection with localization model...")
    print(f"   Window size: {window_size}x{window_size}")
    print(f"   Stride: {stride}")
    print(f"   Confidence threshold: {threshold}")
    
    detections = []
    img_height, img_width = image.shape[:2]
    
    # Ensure image is in correct format
    if image.max() > 1.0:
        image = image.astype(np.float32) / 255.0
    
    patch_count = 0
    for y in range(0, img_height - window_size + 1, stride):
        for x in range(0, img_width - window_size + 1, stride):
            # Extract patch
            patch = image[y:y+window_size, x:x+window_size]
            
            # Ensure patch is the right size
            if patch.shape[:2] != (window_size, window_size):
                continue
                
            # Preprocess patch
            patch_input = np.expand_dims(patch, axis=0)
            
            # Make prediction using current model (returns class_pred, bbox_pred)
            class_pred, bbox_pred = model.predict(patch_input, verbose=0)
            class_id = np.argmax(class_pred[0])
            confidence = np.max(class_pred[0])
            
            # Get bounding box prediction (YOLO format: cx, cy, w, h)
            center_x, center_y, width, height = bbox_pred[0]
            
            patch_count += 1
            
            # If confidence above threshold, save detection
            if confidence > threshold:
                # Convert relative bbox to absolute coordinates within the patch
                abs_center_x = x + (center_x * window_size)
                abs_center_y = y + (center_y * window_size)
                abs_width = width * window_size
                abs_height = height * window_size
                
                # Convert to corner coordinates for visualization
                bbox_x = abs_center_x - abs_width/2
                bbox_y = abs_center_y - abs_height/2
                
                detections.append({
                    'x': int(bbox_x),
                    'y': int(bbox_y),
                    'width': int(abs_width),
                    'height': int(abs_height),
                    'class_id': class_id,
                    'class_name': CLASS_NAMES[class_id],
                    'confidence': confidence,
                    'center_x': abs_center_x,
                    'center_y': abs_center_y,
                    'patch_x': x,
                    'patch_y': y
                })
    
    print(f"   Processed {patch_count} patches")
    print(f"   Found {len(detections)} detections above threshold")
    
    return detections

def visualize_detections_with_localization(image, detections, title="Sliding Window Detection with Localization"):
    """Visualize detections using both patch location and predicted bounding box"""
    plt.figure(figsize=(15, 10))
    
    # Display image
    if image.max() <= 1.0:
        plt.imshow(image)
    else:
        plt.imshow(image.astype(np.uint8))
    
    # Draw bounding boxes
    ax = plt.gca()
    colors = ['red', 'blue', 'green', 'yellow', 'purple']
    
    for i, detection in enumerate(detections):
        x, y = detection['x'], detection['y']
        w, h = detection['width'], detection['height']
        patch_x, patch_y = detection['patch_x'], detection['patch_y']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Choose color based on class
        color = colors[detection['class_id'] % len(colors)]
        
        # Draw predicted bounding box (solid line)
        rect = plt.Rectangle((x, y), w, h, fill=False, edgecolor=color, linewidth=3, linestyle='-')
        ax.add_patch(rect)
        
        # Draw patch window (dashed line) for reference
        patch_rect = plt.Rectangle((patch_x, patch_y), 150, 150, fill=False, edgecolor=color, linewidth=1, linestyle='--', alpha=0.5)
        ax.add_patch(patch_rect)
        
        # Add text label
        label = f"{class_name} ({confidence:.2f})"
        plt.text(x, y-5, label, bbox=dict(boxstyle='round', facecolor=color, alpha=0.8),
                fontsize=10, color='white', weight='bold')
    
    plt.title(f"{title} - {len(detections)} detections")
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    return detections

def non_max_suppression_localization(detections, iou_threshold=0.3):
    """Apply Non-Maximum Suppression using predicted bounding boxes"""
    if not detections:
        return []
    
    # Sort by confidence
    detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
    
    def calculate_iou(box1, box2):
        """Calculate Intersection over Union using center coordinates"""
        # Convert to corner coordinates
        x1_1 = box1['center_x'] - box1['width']/2
        y1_1 = box1['center_y'] - box1['height']/2
        x2_1 = box1['center_x'] + box1['width']/2
        y2_1 = box1['center_y'] + box1['height']/2
        
        x1_2 = box2['center_x'] - box2['width']/2
        y1_2 = box2['center_y'] - box2['height']/2
        x2_2 = box2['center_x'] + box2['width']/2
        y2_2 = box2['center_y'] + box2['height']/2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = box1['width'] * box1['height']
        area2 = box2['width'] * box2['height']
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    filtered_detections = []
    
    for detection in detections:
        # Check if this detection overlaps significantly with any already selected
        keep = True
        for selected in filtered_detections:
            if calculate_iou(detection, selected) > iou_threshold:
                keep = False
                break
        
        if keep:
            filtered_detections.append(detection)
    
    print(f"NMS: {len(detections)} -> {len(filtered_detections)} detections")
    return filtered_detections

def load_and_resize_image(image_path, target_size=(400, 400)):
    """Load and resize image for sliding window detection"""
    try:
        img = load_img(image_path)
        img_array = img_to_array(img)
        
        # Resize image
        test_image = tf.image.resize(img_array, target_size).numpy()
        
        print(f"📸 Loaded image: {image_path}")
        print(f"   Original size: {img_array.shape[:2]}")
        print(f"   Resized to: {test_image.shape[:2]}")
        
        return test_image
        
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        return None

# Set your test image path here
test_image_path = r"D:\Backup\Recap\Test\test2.png"

# Fallback to dataset image if custom image not found
if not os.path.exists(test_image_path):
    print("Custom image not found, searching dataset...")
    for split in ['test', 'val', 'train']:
        for class_name in ['corn', 'pineapple']:
            class_dir = os.path.join(DATASET_PATH, split, class_name)
            if os.path.exists(class_dir):
                image_files = [f for f in os.listdir(class_dir) 
                              if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                if image_files:
                    test_image_path = os.path.join(class_dir, image_files[0])
                    break
        if test_image_path and os.path.exists(test_image_path):
            break

print(f"Using image: {test_image_path}")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        # Run sliding window detection with correct window size
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=250,  # Changed from 150 to 250 to match model input
            stride=125,       # Adjusted stride accordingly (half of window size)
            threshold=0.5
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION 7: RUN SLIDING WINDOW DETECTION DEMO
# ============================================================================

if model is not None and test_image_path and os.path.exists(test_image_path):
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Load and resize image
    test_image = load_and_resize_image(test_image_path, target_size=(400, 400))
    
    if test_image is not None:
        # Run sliding window detection
        detections = sliding_window_detection_with_localization(
            model,
            test_image, 
            window_size=150, 
            stride=50,  # Smaller stride for better coverage
            threshold=0.5  # Lower threshold to get more detections
        )
        
        # Visualize raw detections
        print("\n📊 Raw detections with localization:")
        visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
        
        # Apply Non-Maximum Suppression
        if detections:
            print("\n🔧 Applying Non-Maximum Suppression...")
            filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
            
            # Visualize filtered detections
            print("\n📊 Filtered detections:")
            visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
            
            # Print detection results in YOLO format
            print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
            for i, det in enumerate(filtered_detections):
                # Convert to normalized YOLO format
                img_h, img_w = test_image.shape[:2]
                norm_cx = det['center_x'] / img_w
                norm_cy = det['center_y'] / img_h
                norm_w = det['width'] / img_w
                norm_h = det['height'] / img_h
                
                print(f"Detection {i+1}:")
                print(f"  pred_class = \"{det['class_name']}\"")
                print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
                print(f"  confidence = {det['confidence']:.3f}")
                print()
        else:
            print("❌ No detections found above threshold")
            print("💡 Try lowering the threshold or using a different image")
    else:
        print("❌ Could not load test image")
else:
    print("❌ Missing requirements:")
    if model is None:
        print("   - Model not loaded")
    if not test_image_path or not os.path.exists(test_image_path):
        print("   - Valid image path not found")
    print("\n💡 Please ensure you have:")
    print("   1. Trained and saved your model")
    print("   2. Set a valid image path") 
    print("   3. Run all previous cells")

# ============================================================================
# SECTION: VISUALIZE ALL POSSIBLE PATCHES
# ============================================================================

def visualize_all_patches(image, window_size=250, stride=125, max_patches_to_show=20):
    """Visualize all possible sliding window patches"""
    print(f"🔍 Visualizing all possible patches...")
    print(f"   Window size: {window_size}x{window_size}")
    print(f"   Stride: {stride}")
    
    img_height, img_width = image.shape[:2]
    
    # Calculate total number of patches
    patches_y = (img_height - window_size) // stride + 1
    patches_x = (img_width - window_size) // stride + 1
    total_patches = patches_y * patches_x
    
    print(f"   Image size: {img_width}x{img_height}")
    print(f"   Total patches: {total_patches} ({patches_x} x {patches_y})")
    
    # Visualize the grid of all patches
    plt.figure(figsize=(15, 10))
    
    # Display image
    if image.max() <= 1.0:
        plt.imshow(image)
    else:
        plt.imshow(image.astype(np.uint8))
    
    ax = plt.gca()
    
    # Draw all patch windows
    patch_count = 0
    colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'brown']
    
    for y in range(0, img_height - window_size + 1, stride):
        for x in range(0, img_width - window_size + 1, stride):
            color = colors[patch_count % len(colors)]
            
            # Draw patch rectangle
            rect = plt.Rectangle((x, y), window_size, window_size, 
                               fill=False, edgecolor=color, linewidth=1, alpha=0.7)
            ax.add_patch(rect)
            
            # Add patch number
            plt.text(x + 5, y + 15, str(patch_count + 1), 
                    fontsize=8, color=color, weight='bold',
                    bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))
            
            patch_count += 1
    
    plt.title(f"All Sliding Window Patches - Total: {total_patches}")
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    return total_patches

def show_individual_patches(image, window_size=250, stride=125, max_patches_to_show=12):
    """Show individual patch samples"""
    print(f"📋 Showing individual patch samples...")
    
    img_height, img_width = image.shape[:2]
    
    # Ensure image is in correct format
    if image.max() > 1.0:
        image = image.astype(np.float32) / 255.0
    
    patches = []
    positions = []
    
    # Collect patches
    patch_count = 0
    for y in range(0, img_height - window_size + 1, stride):
        for x in range(0, img_width - window_size + 1, stride):
            if patch_count >= max_patches_to_show:
                break
                
            # Extract patch
            patch = image[y:y+window_size, x:x+window_size]
            
            if patch.shape[:2] == (window_size, window_size):
                patches.append(patch)
                positions.append((x, y, patch_count + 1))
                patch_count += 1
        
        if patch_count >= max_patches_to_show:
            break
    
    # Display patches in a grid
    cols = 4
    rows = (len(patches) + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(15, rows * 3))
    if rows == 1:
        axes = axes.reshape(1, -1)
    
    for i, (patch, (x, y, num)) in enumerate(zip(patches, positions)):
        row = i // cols
        col = i % cols
        
        axes[row, col].imshow(patch)
        axes[row, col].set_title(f"Patch {num}\nPos: ({x}, {y})")
        axes[row, col].axis('off')
    
    # Hide empty subplots
    for i in range(len(patches), rows * cols):
        row = i // cols
        col = i % cols
        axes[row, col].axis('off')
    
    plt.suptitle(f"Individual Patches (showing first {len(patches)})")
    plt.tight_layout()
    plt.show()
    
    return patches, positions

# Run the patch visualization
if test_image_path and os.path.exists(test_image_path):
    print("🎯 ALL POSSIBLE PATCHES VISUALIZATION")
    print("=" * 50)
    
    # Load image
    test_image = load_and_resize_image(test_image_path, target_size=(500, 500))  # Larger for better visualization
    
    if test_image is not None:
        # Show all patch locations
        total_patches = visualize_all_patches(test_image, window_size=250, stride=125)
        
        # Show individual patch samples
        patches, positions = show_individual_patches(test_image, window_size=250, stride=125, max_patches_to_show=12)
        
        print(f"\n📊 PATCH STATISTICS:")
        print(f"   Total patches to process: {total_patches}")
        print(f"   Patch size: 250x250 pixels")
        print(f"   Stride: 125 pixels")
        print(f"   Overlap: 50% (125/250)")
        print(f"   Processing time estimate: ~{total_patches * 0.1:.1f} seconds")
        
        # Calculate computational complexity
        print(f"\n⚡ COMPUTATIONAL ANALYSIS:")
        print(f"   Each patch requires: 1 forward pass through CNN")
        print(f"   Total forward passes: {total_patches}")
        print(f"   Memory per patch: ~{250*250*3*4/1024/1024:.1f} MB")
        print(f"   This is a brute-force O(n²) approach")
        
    else:
        print("❌ Could not load image for patch visualization")
else:
    print("❌ No valid image path for patch visualization")