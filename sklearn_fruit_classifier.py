import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import seaborn as sns

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 64  # Smaller for faster processing with sklearn

def load_images_from_folder(folder_path, label, max_images=None):
    """Load images from a folder and assign labels"""
    images = []
    labels = []
    
    image_files = os.listdir(folder_path)
    if max_images:
        image_files = image_files[:max_images]
    
    print(f"Loading {len(image_files)} images from {folder_path}...")
    
    for i, filename in enumerate(image_files):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                # Load and resize image
                img = Image.open(img_path)
                img = img.convert('RGB')
                img = img.resize((IMG_SIZE, IMG_SIZE))
                img_array = np.array(img)
                
                # Flatten image for sklearn (convert to 1D feature vector)
                img_flattened = img_array.flatten()
                
                images.append(img_flattened)
                labels.append(label)
                
                if (i + 1) % 100 == 0:
                    print(f"  Loaded {i + 1}/{len(image_files)} images")
                    
            except Exception as e:
                print(f"Error loading {filename}: {e}")
    
    return np.array(images), np.array(labels)

def load_dataset():
    """Load the complete dataset"""
    print("Loading fruit classification dataset...")
    
    # Load training data
    train_corn_images, train_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'corn'), 0
    )
    train_pineapple_images, train_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1
    )
    
    # Load validation data
    val_corn_images, val_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'corn'), 0
    )
    val_pineapple_images, val_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1
    )
    
    # Load test data
    test_corn_images, test_corn_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'corn'), 0
    )
    test_pineapple_images, test_pineapple_labels = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1
    )
    
    # Combine training and validation data
    X_train = np.concatenate([train_corn_images, train_pineapple_images, 
                             val_corn_images, val_pineapple_images])
    y_train = np.concatenate([train_corn_labels, train_pineapple_labels,
                             val_corn_labels, val_pineapple_labels])
    
    # Test data
    X_test = np.concatenate([test_corn_images, test_pineapple_images])
    y_test = np.concatenate([test_corn_labels, test_pineapple_labels])
    
    # Shuffle training data
    train_indices = np.random.permutation(len(X_train))
    X_train = X_train[train_indices]
    y_train = y_train[train_indices]
    
    print(f"Training set: {len(X_train)} images")
    print(f"Test set: {len(X_test)} images")
    print(f"Feature vector size: {X_train.shape[1]} (flattened {IMG_SIZE}x{IMG_SIZE}x3 image)")
    
    return X_train, y_train, X_test, y_test

def normalize_data(X_train, X_test):
    """Normalize the data using StandardScaler"""
    print("Normalizing data...")
    
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    return X_train_scaled, X_test_scaled, scaler

def train_random_forest(X_train, y_train):
    """Train a Random Forest classifier"""
    print("Training Random Forest classifier...")
    
    rf_model = RandomForestClassifier(
        n_estimators=100,
        max_depth=20,
        random_state=42,
        n_jobs=-1,
        verbose=1
    )
    
    rf_model.fit(X_train, y_train)
    
    print("Random Forest training completed!")
    return rf_model

def train_svm(X_train, y_train):
    """Train an SVM classifier"""
    print("Training SVM classifier...")
    
    svm_model = SVC(
        kernel='rbf',
        C=1.0,
        random_state=42,
        probability=True
    )
    
    svm_model.fit(X_train, y_train)
    
    print("SVM training completed!")
    return svm_model

def evaluate_model(model, X_test, y_test, model_name="Model"):
    """Evaluate the model"""
    print(f"Evaluating {model_name}...")
    
    # Make predictions
    y_pred = model.predict(X_test)
    
    # Calculate accuracy
    accuracy = accuracy_score(y_test, y_pred)
    print(f"{model_name} Accuracy: {accuracy:.4f}")
    
    # Classification report
    class_names = ['corn', 'pineapple']
    print(f"\n{model_name} Classification Report:")
    print(classification_report(y_test, y_pred, target_names=class_names))
    
    # Confusion matrix
    cm = confusion_matrix(y_test, y_pred)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
               xticklabels=class_names, yticklabels=class_names)
    plt.title(f'{model_name} Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()
    
    return accuracy

def show_sample_images(X_train, y_train, class_names=['corn', 'pineapple']):
    """Show sample images from training set"""
    print("Showing sample images from training set...")
    
    plt.figure(figsize=(12, 6))
    
    # Show 4 corn images
    corn_indices = np.where(y_train == 0)[0][:4]
    for i, idx in enumerate(corn_indices):
        plt.subplot(2, 4, i + 1)
        # Reshape flattened image back to original shape
        img = X_train[idx].reshape(IMG_SIZE, IMG_SIZE, 3).astype(np.uint8)
        plt.imshow(img)
        plt.title(f'{class_names[0].capitalize()}')
        plt.axis('off')
    
    # Show 4 pineapple images
    pineapple_indices = np.where(y_train == 1)[0][:4]
    for i, idx in enumerate(pineapple_indices):
        plt.subplot(2, 4, i + 5)
        # Reshape flattened image back to original shape
        img = X_train[idx].reshape(IMG_SIZE, IMG_SIZE, 3).astype(np.uint8)
        plt.imshow(img)
        plt.title(f'{class_names[1].capitalize()}')
        plt.axis('off')
    
    plt.tight_layout()
    plt.show()

def test_predictions(model, X_test, y_test, model_name="Model", num_tests=10):
    """Test random predictions"""
    print(f"Testing {num_tests} random predictions with {model_name}...")
    
    # Get random samples
    indices = np.random.choice(len(X_test), num_tests, replace=False)
    correct = 0
    
    class_names = ['corn', 'pineapple']
    
    for i, idx in enumerate(indices):
        # Make prediction
        prediction = model.predict([X_test[idx]])[0]
        predicted_class = class_names[prediction]
        true_class = class_names[y_test[idx]]
        
        # Get confidence if available
        if hasattr(model, 'predict_proba'):
            proba = model.predict_proba([X_test[idx]])[0]
            confidence = np.max(proba)
        else:
            confidence = 1.0  # Random Forest doesn't have predict_proba by default
        
        is_correct = predicted_class == true_class
        if is_correct:
            correct += 1
            
        print(f"Test {i+1:2d}: True={true_class:9s} | Pred={predicted_class:9s} | Conf={confidence:.2f} | {'✓' if is_correct else '✗'}")
    
    accuracy = correct / num_tests
    print(f"\n{model_name} random test accuracy: {accuracy:.2f} ({correct}/{num_tests})")

def compare_models(X_train, y_train, X_test, y_test):
    """Compare different models"""
    print("Comparing different models...")
    
    models = {}
    
    # Train Random Forest
    rf_model = train_random_forest(X_train, y_train)
    models['Random Forest'] = rf_model
    
    # Train SVM (on a subset for speed)
    print("Training SVM on subset of data for speed...")
    subset_size = min(2000, len(X_train))
    subset_indices = np.random.choice(len(X_train), subset_size, replace=False)
    X_train_subset = X_train[subset_indices]
    y_train_subset = y_train[subset_indices]
    
    svm_model = train_svm(X_train_subset, y_train_subset)
    models['SVM'] = svm_model
    
    # Evaluate all models
    results = {}
    for name, model in models.items():
        accuracy = evaluate_model(model, X_test, y_test, name)
        results[name] = accuracy
        test_predictions(model, X_test, y_test, name, num_tests=5)
        print("-" * 50)
    
    # Show comparison
    print("Model Comparison:")
    for name, accuracy in results.items():
        print(f"{name}: {accuracy:.4f}")
    
    return models, results

def main():
    """Main function"""
    print("🍍 Scikit-Learn Fruit Classifier - Corn vs Pineapple 🌽")
    print("=" * 60)
    
    # Set random seed
    np.random.seed(42)
    
    # Load dataset
    X_train, y_train, X_test, y_test = load_dataset()
    
    # Show sample images
    show_sample_images(X_train, y_train)
    
    # Normalize data
    X_train_scaled, X_test_scaled, scaler = normalize_data(X_train, X_test)
    
    # Compare models
    models, results = compare_models(X_train_scaled, y_train, X_test_scaled, y_test)
    
    # Get best model
    best_model_name = max(results, key=results.get)
    best_model = models[best_model_name]
    best_accuracy = results[best_model_name]
    
    print(f"\n🎉 Best model: {best_model_name} with accuracy: {best_accuracy:.4f}")

if __name__ == "__main__":
    main()
