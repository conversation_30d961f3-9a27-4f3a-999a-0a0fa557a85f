"""
Sliding Window Object Detection for Fruit Classifier
Using our existing trained model with Keras instead of OpenCV
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# Configuration (should match your main notebook)
IMG_SIZE = 150
CLASS_NAMES = ['corn', 'pineapple']

def create_classification_model():
    """Create a simple classification model for sliding window detection"""
    print("🏗️ Creating classification model for sliding window...")
    
    # Input layer
    input_layer = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))
    
    # Convolutional blocks
    x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(input_layer)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)
    
    # Dense layers - only classification output
    x = tf.keras.layers.Flatten()(x)
    x = tf.keras.layers.Dense(128, activation='relu')(x)
    x = tf.keras.layers.Dropout(0.5)(x)
    class_output = tf.keras.layers.Dense(2, activation='softmax')(x)  # Only classification
    
    model = tf.keras.Model(inputs=input_layer, outputs=class_output)
    
    model.compile(
        optimizer='adam',
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    print("✅ Classification model created!")
    return model

def sliding_window_detection(model, image, window_size=150, stride=75, threshold=0.7):
    """Perform sliding window object detection using Keras"""
    print(f"🔍 Performing sliding window detection...")
    print(f"   Window size: {window_size}x{window_size}")
    print(f"   Stride: {stride}")
    print(f"   Confidence threshold: {threshold}")
    
    detections = []
    img_height, img_width = image.shape[:2]
    
    # Ensure image is in correct format
    if image.max() > 1.0:
        image = image.astype(np.float32) / 255.0
    
    patch_count = 0
    for y in range(0, img_height - window_size + 1, stride):
        for x in range(0, img_width - window_size + 1, stride):
            # Extract patch
            patch = image[y:y+window_size, x:x+window_size]
            
            # Ensure patch is the right size
            if patch.shape[:2] != (window_size, window_size):
                continue
                
            # Preprocess patch
            patch_input = np.expand_dims(patch, axis=0)
            
            # Make prediction
            pred = model.predict(patch_input, verbose=0)
            class_id = np.argmax(pred[0])
            confidence = pred[0][class_id]
            
            patch_count += 1
            
            # If confidence above threshold, save detection
            if confidence > threshold:
                detections.append({
                    'x': x,
                    'y': y,
                    'width': window_size,
                    'height': window_size,
                    'class_id': class_id,
                    'class_name': CLASS_NAMES[class_id],
                    'confidence': confidence
                })
    
    print(f"   Processed {patch_count} patches")
    print(f"   Found {len(detections)} detections above threshold")
    
    return detections

def visualize_detections(image, detections, title="Sliding Window Detection"):
    """Visualize detections on the image using matplotlib"""
    plt.figure(figsize=(12, 8))
    
    # Display image
    if image.max() <= 1.0:
        plt.imshow(image)
    else:
        plt.imshow(image.astype(np.uint8))
    
    # Draw bounding boxes
    ax = plt.gca()
    colors = ['red', 'blue', 'green', 'yellow', 'purple']
    
    for i, detection in enumerate(detections):
        x, y = detection['x'], detection['y']
        w, h = detection['width'], detection['height']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Choose color based on class
        color = colors[detection['class_id'] % len(colors)]
        
        # Draw rectangle
        rect = plt.Rectangle((x, y), w, h, fill=False, edgecolor=color, linewidth=2)
        ax.add_patch(rect)
        
        # Add text label
        label = f"{class_name} ({confidence:.2f})"
        plt.text(x, y-5, label, bbox=dict(boxstyle='round', facecolor=color, alpha=0.8),
                fontsize=10, color='white', weight='bold')
    
    plt.title(f"{title} - {len(detections)} detections")
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    return detections

def non_max_suppression(detections, iou_threshold=0.3):
    """Apply Non-Maximum Suppression to remove overlapping detections"""
    if not detections:
        return []
    
    # Sort by confidence
    detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
    
    def calculate_iou(box1, box2):
        """Calculate Intersection over Union"""
        x1_1, y1_1 = box1['x'], box1['y']
        x2_1, y2_1 = x1_1 + box1['width'], y1_1 + box1['height']
        
        x1_2, y1_2 = box2['x'], box2['y']
        x2_2, y2_2 = x1_2 + box2['width'], y1_2 + box2['height']
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = box1['width'] * box1['height']
        area2 = box2['width'] * box2['height']
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    filtered_detections = []
    
    for detection in detections:
        # Check if this detection overlaps significantly with any already selected
        keep = True
        for selected in filtered_detections:
            if calculate_iou(detection, selected) > iou_threshold:
                keep = False
                break
        
        if keep:
            filtered_detections.append(detection)
    
    print(f"NMS: {len(detections)} -> {len(filtered_detections)} detections")
    return filtered_detections

def load_and_resize_image(image_path, target_size=(300, 300)):
    """Load and resize image for sliding window detection"""
    try:
        # Load image
        img = load_img(image_path)
        img_array = img_to_array(img)
        
        # Resize image
        img_resized = tf.image.resize(img_array, target_size)
        img_resized = img_resized.numpy()
        
        print(f"📸 Loaded image: {image_path}")
        print(f"   Original size: {img_array.shape[:2]}")
        print(f"   Resized to: {img_resized.shape[:2]}")
        
        return img_resized
        
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        return None

def demo_sliding_window_detection(model, image_path):
    """Demo function to run sliding window detection on an image"""
    print("🎯 SLIDING WINDOW DETECTION DEMO")
    print("=" * 50)
    
    # Load and resize image
    test_image = load_and_resize_image(image_path, target_size=(300, 300))
    if test_image is None:
        return
    
    # Run sliding window detection
    detections = sliding_window_detection(
        model, 
        test_image, 
        window_size=150, 
        stride=75, 
        threshold=0.7
    )
    
    # Visualize raw detections
    print("\n📊 Raw detections:")
    visualize_detections(test_image, detections, "Raw Sliding Window Detections")
    
    # Apply Non-Maximum Suppression
    if detections:
        print("\n🔧 Applying Non-Maximum Suppression...")
        filtered_detections = non_max_suppression(detections, iou_threshold=0.3)
        
        # Visualize filtered detections
        print("\n📊 Filtered detections:")
        visualize_detections(test_image, filtered_detections, "After Non-Maximum Suppression")
        
        # Print detection results
        print(f"\n📋 DETECTION RESULTS:")
        for i, det in enumerate(filtered_detections):
            print(f"Detection {i+1}:")
            print(f"  Class: {det['class_name']}")
            print(f"  Confidence: {det['confidence']:.3f}")
            print(f"  Bounding Box: ({det['x']}, {det['y']}, {det['width']}, {det['height']})")
    else:
        print("❌ No detections found above threshold")

if __name__ == "__main__":
    print("🚀 Sliding Window Detection Module Loaded!")
    print("Use demo_sliding_window_detection(model, image_path) to test")
