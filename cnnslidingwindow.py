# brute-force object detection demo using sliding window + CNN

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import load_img, img_to_array

# Configuration (should match your main notebook)
IMG_SIZE = 150
CLASS_NAMES = ['corn', 'pineapple']
DATASET_PATH = "FruitDetection"

def sliding_window_detection_with_localization(model, image, window_size=150, stride=75, threshold=0.7):
    """Perform sliding window object detection using our current localization model"""
    print(f"🔍 Performing sliding window detection with localization model...")
    print(f"   Window size: {window_size}x{window_size}")
    print(f"   Stride: {stride}")
    print(f"   Confidence threshold: {threshold}")
    
    detections = []
    img_height, img_width = image.shape[:2]
    
    # Ensure image is in correct format
    if image.max() > 1.0:
        image = image.astype(np.float32) / 255.0
    
    patch_count = 0
    for y in range(0, img_height - window_size + 1, stride):
        for x in range(0, img_width - window_size + 1, stride):
            # Extract patch
            patch = image[y:y+window_size, x:x+window_size]
            
            # Ensure patch is the right size
            if patch.shape[:2] != (window_size, window_size):
                continue
                
            # Preprocess patch
            patch_input = np.expand_dims(patch, axis=0)
            
            # Make prediction using current model (returns class_pred, bbox_pred)
            class_pred, bbox_pred = model.predict(patch_input, verbose=0)
            class_id = np.argmax(class_pred[0])
            confidence = np.max(class_pred[0])
            
            # Get bounding box prediction (YOLO format: cx, cy, w, h)
            center_x, center_y, width, height = bbox_pred[0]
            
            patch_count += 1
            
            # If confidence above threshold, save detection
            if confidence > threshold:
                # Convert relative bbox to absolute coordinates within the patch
                abs_center_x = x + (center_x * window_size)
                abs_center_y = y + (center_y * window_size)
                abs_width = width * window_size
                abs_height = height * window_size
                
                # Convert to corner coordinates for visualization
                bbox_x = abs_center_x - abs_width/2
                bbox_y = abs_center_y - abs_height/2
                
                detections.append({
                    'x': int(bbox_x),
                    'y': int(bbox_y),
                    'width': int(abs_width),
                    'height': int(abs_height),
                    'class_id': class_id,
                    'class_name': CLASS_NAMES[class_id],
                    'confidence': confidence,
                    'center_x': abs_center_x,
                    'center_y': abs_center_y,
                    'patch_x': x,
                    'patch_y': y
                })
    
    print(f"   Processed {patch_count} patches")
    print(f"   Found {len(detections)} detections above threshold")
    
    return detections

def visualize_detections_with_localization(image, detections, title="Sliding Window Detection with Localization"):
    """Visualize detections using both patch location and predicted bounding box"""
    plt.figure(figsize=(15, 10))
    
    # Display image
    if image.max() <= 1.0:
        plt.imshow(image)
    else:
        plt.imshow(image.astype(np.uint8))
    
    # Draw bounding boxes
    ax = plt.gca()
    colors = ['red', 'blue', 'green', 'yellow', 'purple']
    
    for i, detection in enumerate(detections):
        x, y = detection['x'], detection['y']
        w, h = detection['width'], detection['height']
        patch_x, patch_y = detection['patch_x'], detection['patch_y']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Choose color based on class
        color = colors[detection['class_id'] % len(colors)]
        
        # Draw predicted bounding box (solid line)
        rect = plt.Rectangle((x, y), w, h, fill=False, edgecolor=color, linewidth=3, linestyle='-')
        ax.add_patch(rect)
        
        # Draw patch window (dashed line) for reference
        patch_rect = plt.Rectangle((patch_x, patch_y), 150, 150, fill=False, edgecolor=color, linewidth=1, linestyle='--', alpha=0.5)
        ax.add_patch(patch_rect)
        
        # Add text label
        label = f"{class_name} ({confidence:.2f})"
        plt.text(x, y-5, label, bbox=dict(boxstyle='round', facecolor=color, alpha=0.8),
                fontsize=10, color='white', weight='bold')
    
    plt.title(f"{title} - {len(detections)} detections")
    plt.axis('off')
    plt.tight_layout()
    plt.show()
    
    return detections

def non_max_suppression_localization(detections, iou_threshold=0.3):
    """Apply Non-Maximum Suppression using predicted bounding boxes"""
    if not detections:
        return []
    
    # Sort by confidence
    detections = sorted(detections, key=lambda x: x['confidence'], reverse=True)
    
    def calculate_iou(box1, box2):
        """Calculate Intersection over Union using center coordinates"""
        # Convert to corner coordinates
        x1_1 = box1['center_x'] - box1['width']/2
        y1_1 = box1['center_y'] - box1['height']/2
        x2_1 = box1['center_x'] + box1['width']/2
        y2_1 = box1['center_y'] + box1['height']/2
        
        x1_2 = box2['center_x'] - box2['width']/2
        y1_2 = box2['center_y'] - box2['height']/2
        x2_2 = box2['center_x'] + box2['width']/2
        y2_2 = box2['center_y'] + box2['height']/2
        
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0.0
        
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = box1['width'] * box1['height']
        area2 = box2['width'] * box2['height']
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0.0
    
    filtered_detections = []
    
    for detection in detections:
        # Check if this detection overlaps significantly with any already selected
        keep = True
        for selected in filtered_detections:
            if calculate_iou(detection, selected) > iou_threshold:
                keep = False
                break
        
        if keep:
            filtered_detections.append(detection)
    
    print(f"NMS: {len(detections)} -> {len(filtered_detections)} detections")
    return filtered_detections

def load_and_resize_image(image_path, target_size=(400, 400)):
    """Load and resize image for sliding window detection"""
    try:
        img = load_img(image_path)
        img_array = img_to_array(img)
        
        # Resize image
        test_image = tf.image.resize(img_array, target_size).numpy()
        
        print(f"📸 Loaded image: {image_path}")
        print(f"   Resized to: {test_image.shape[:2]}")
        
        return test_image
        
    except Exception as e:
        print(f"❌ Error loading image: {e}")
        return None

def run_sliding_window_demo(model, image_path=None):
    """Main demo function for sliding window detection"""
    print("🎯 BRUTE-FORCE OBJECT DETECTION DEMO")
    print("=" * 60)
    print("Using sliding window + CNN with localization")
    print("=" * 60)
    
    # Use provided image path or find one from dataset
    if image_path is None or not os.path.exists(image_path):
        print("Searching for test image in dataset...")
        for split in ['test', 'val', 'train']:
            for class_name in ['corn', 'pineapple']:
                class_dir = os.path.join(DATASET_PATH, split, class_name)
                if os.path.exists(class_dir):
                    image_files = [f for f in os.listdir(class_dir) 
                                  if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                    if image_files:
                        image_path = os.path.join(class_dir, image_files[0])
                        break
            if image_path:
                break
    
    if not image_path or not os.path.exists(image_path):
        print("❌ No valid image found")
        return
    
    # Load and resize image
    test_image = load_and_resize_image(image_path, target_size=(400, 400))
    if test_image is None:
        return
    
    # Run sliding window detection
    detections = sliding_window_detection_with_localization(
        model,
        test_image, 
        window_size=150, 
        stride=50,  # Smaller stride for better coverage
        threshold=0.5  # Lower threshold to get more detections
    )
    
    # Visualize raw detections
    print("\n📊 Raw detections with localization:")
    visualize_detections_with_localization(test_image, detections, "Raw Sliding Window + Localization")
    
    # Apply Non-Maximum Suppression
    if detections:
        print("\n🔧 Applying Non-Maximum Suppression...")
        filtered_detections = non_max_suppression_localization(detections, iou_threshold=0.4)
        
        # Visualize filtered detections
        print("\n📊 Filtered detections:")
        visualize_detections_with_localization(test_image, filtered_detections, "After NMS + Localization")
        
        # Print detection results in YOLO format
        print(f"\n📋 DETECTION RESULTS (YOLO FORMAT):")
        for i, det in enumerate(filtered_detections):
            # Convert to normalized YOLO format
            img_h, img_w = test_image.shape[:2]
            norm_cx = det['center_x'] / img_w
            norm_cy = det['center_y'] / img_h
            norm_w = det['width'] / img_w
            norm_h = det['height'] / img_h
            
            print(f"Detection {i+1}:")
            print(f"  pred_class = \"{det['class_name']}\"")
            print(f"  pred_bbox = [{norm_cx:.3f}, {norm_cy:.3f}, {norm_w:.3f}, {norm_h:.3f}]  # [cx, cy, w, h]")
            print(f"  confidence = {det['confidence']:.3f}")
            print()
        
        return filtered_detections
    else:
        print("❌ No detections found above threshold")
        print("💡 Try lowering the threshold or using a different image")
        return []

if __name__ == "__main__":
    print("🚀 CNN Sliding Window Detection Module")
    print("Import this module and call run_sliding_window_demo(model, image_path)")
