import tensorflow as tf
import matplotlib.pyplot as plt
import numpy as np
import os
import random
from fruit_classifier import FruitClassifier

def explore_dataset(dataset_path="FruitDetection"):
    """Explore the dataset and show sample images"""
    print("📊 Dataset Exploration")
    print("=" * 30)
    
    # Count images in each split
    for split in ['train', 'val', 'test']:
        split_path = os.path.join(dataset_path, split)
        if os.path.exists(split_path):
            corn_count = len(os.listdir(os.path.join(split_path, 'corn')))
            pineapple_count = len(os.listdir(os.path.join(split_path, 'pineapple')))
            total = corn_count + pineapple_count
            print(f"{split.upper()} SET:")
            print(f"  Corn: {corn_count} images")
            print(f"  Pineapple: {pineapple_count} images")
            print(f"  Total: {total} images")
            print()

def show_sample_images(dataset_path="FruitDetection", num_samples=8):
    """Show sample images from the dataset"""
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    fig.suptitle('Sample Images from Dataset', fontsize=16)
    
    classes = ['corn', 'pineapple']
    
    for i, class_name in enumerate(classes):
        class_path = os.path.join(dataset_path, 'train', class_name)
        image_files = os.listdir(class_path)
        
        # Select random images
        selected_images = random.sample(image_files, 4)
        
        for j, img_file in enumerate(selected_images):
            img_path = os.path.join(class_path, img_file)
            img = tf.keras.preprocessing.image.load_img(img_path, target_size=(224, 224))
            
            axes[i, j].imshow(img)
            axes[i, j].set_title(f'{class_name.capitalize()}')
            axes[i, j].axis('off')
    
    plt.tight_layout()
    plt.show()

def quick_train_and_test():
    """Quick training and testing of the classifier"""
    print("🚀 Quick Training and Testing")
    print("=" * 35)
    
    # Initialize classifier
    classifier = FruitClassifier()
    
    # Setup data
    classifier.setup_data_generators()
    
    # Build a simple model for quick testing
    print("\nBuilding simple CNN model...")
    classifier.build_model(use_pretrained=False)
    
    # Train for fewer epochs for quick testing
    print("\nTraining model (quick test - 5 epochs)...")
    classifier.train_model(epochs=5)
    
    # Plot training history
    classifier.plot_training_history()
    
    # Evaluate
    classifier.evaluate_model()
    
    return classifier

def test_pretrained_model():
    """Train with pretrained MobileNetV2 for better accuracy"""
    print("🎯 Training with Pretrained MobileNetV2")
    print("=" * 40)
    
    # Initialize classifier
    classifier = FruitClassifier()
    
    # Setup data
    classifier.setup_data_generators()
    
    # Build pretrained model
    print("\nBuilding MobileNetV2-based model...")
    classifier.build_model(use_pretrained=True)
    
    # Train
    print("\nTraining model...")
    classifier.train_model(epochs=10)
    
    # Plot training history
    classifier.plot_training_history()
    
    # Evaluate
    classifier.evaluate_model()
    
    # Save model
    classifier.save_model("fruit_classifier_mobilenet.h5")
    
    return classifier

def test_single_prediction(classifier, dataset_path="FruitDetection"):
    """Test prediction on a random image"""
    print("🔍 Testing Single Prediction")
    print("=" * 30)
    
    # Get a random test image
    test_path = os.path.join(dataset_path, 'test')
    classes = ['corn', 'pineapple']
    
    # Select random class and image
    random_class = random.choice(classes)
    class_path = os.path.join(test_path, random_class)
    image_files = os.listdir(class_path)
    random_image = random.choice(image_files)
    image_path = os.path.join(class_path, random_image)
    
    print(f"Testing image: {random_image}")
    print(f"True class: {random_class}")
    
    # Make prediction
    predicted_class, confidence = classifier.predict_image(image_path)
    
    print(f"Predicted class: {predicted_class}")
    print(f"Confidence: {confidence:.2f}")
    
    # Check if prediction is correct
    correct = predicted_class.lower() == random_class.lower()
    print(f"Prediction correct: {correct}")
    
    return correct

def interactive_menu():
    """Interactive menu for testing the classifier"""
    classifier = None
    
    while True:
        print("\n" + "="*50)
        print("🍍 FRUIT CLASSIFIER MENU 🌽")
        print("="*50)
        print("1. Explore Dataset")
        print("2. Show Sample Images")
        print("3. Quick Train & Test (Simple CNN)")
        print("4. Full Training (MobileNetV2)")
        print("5. Test Single Prediction")
        print("6. Load Saved Model")
        print("7. Exit")
        print("="*50)
        
        choice = input("Enter your choice (1-7): ").strip()
        
        if choice == '1':
            explore_dataset()
            
        elif choice == '2':
            show_sample_images()
            
        elif choice == '3':
            classifier = quick_train_and_test()
            
        elif choice == '4':
            classifier = test_pretrained_model()
            
        elif choice == '5':
            if classifier is None:
                print("❌ No model loaded. Please train a model first (option 3 or 4).")
            else:
                test_single_prediction(classifier)
                
        elif choice == '6':
            try:
                classifier = FruitClassifier()
                classifier.load_model("fruit_classifier_mobilenet.h5")
                print("✅ Model loaded successfully!")
            except:
                print("❌ Could not load model. Make sure you have trained and saved a model first.")
                
        elif choice == '7':
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice. Please enter a number between 1-7.")

if __name__ == "__main__":
    # Set random seed for reproducibility
    random.seed(42)
    tf.random.set_seed(42)
    np.random.seed(42)
    
    # Run interactive menu
    interactive_menu()
