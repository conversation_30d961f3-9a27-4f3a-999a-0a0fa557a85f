import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import os
from tensorflow.keras.preprocessing.image import load_img, img_to_array
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

print("🔧 Using TensorFlow for image classification with localization")
print(f"TensorFlow version: {tf.__version__}")

# Configuration
DATASET_PATH = "FruitDetection"
IMG_SIZE = 250
CLASS_NAMES = ['corn', 'pineapple']  # Binary classification

# Set random seeds
tf.random.set_seed(42)
np.random.seed(42)

def load_images_from_folder(folder_path, label):
    """Load images from a folder and assign labels with synthetic bounding boxes"""
    images = []
    class_labels = []
    bbox_labels = []
    
    image_files = os.listdir(folder_path)
    print(f"Loading {len(image_files)} images from {folder_path}...")
    
    for i, filename in enumerate(image_files):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            img_path = os.path.join(folder_path, filename)
            try:
                img = load_img(img_path, target_size=(IMG_SIZE, IMG_SIZE))
                img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]
                
                images.append(img_array)
                
                # Create one-hot encoded class label (corn=0, pineapple=1)
                class_one_hot = np.zeros(2)
                class_one_hot[label] = 1  # Direct mapping: corn=0, pineapple=1
                class_labels.append(class_one_hot)
                
                # Generate YOLO format bounding box: [center_x, center_y, width, height]
                # Different fruits have different typical positions and sizes
                if label == 0:  # corn
                    # Corn is often elongated and can be positioned variably
                    center_x = np.random.uniform(0.3, 0.7)
                    center_y = np.random.uniform(0.3, 0.7)
                    width = np.random.uniform(0.3, 0.5)
                    height = np.random.uniform(0.4, 0.6)
                else:  # pineapple
                    # Pineapple is more round and centered
                    center_x = np.random.uniform(0.35, 0.65)
                    center_y = np.random.uniform(0.35, 0.65)
                    width = np.random.uniform(0.4, 0.6)
                    height = np.random.uniform(0.4, 0.6)
                
                # Ensure values are within bounds
                center_x = np.clip(center_x, width/2, 1 - width/2)
                center_y = np.clip(center_y, height/2, 1 - height/2)
                
                # Store in YOLO format: [center_x, center_y, width, height]
                bbox_labels.append([center_x, center_y, width, height])
                
                if (i + 1) % 100 == 0:
                    print(f"  Loaded {i + 1}/{len(image_files)} images")
                    
            except Exception as e:
                print(f"Error loading {filename}: {e}")
    
    return np.array(images), np.array(class_labels), np.array(bbox_labels)

def load_dataset():
    """Load the complete dataset with localization labels"""
    print("🍍 Loading fruit classification dataset with localization...")

    # Load training data (train + val)
    train_corn_images, train_corn_class, train_corn_bbox = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'corn'), 0
    )
    train_pineapple_images, train_pineapple_class, train_pineapple_bbox = load_images_from_folder(
        os.path.join(DATASET_PATH, 'train', 'pineapple'), 1
    )
    
    val_corn_images, val_corn_class, val_corn_bbox = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'corn'), 0
    )
    val_pineapple_images, val_pineapple_class, val_pineapple_bbox = load_images_from_folder(
        os.path.join(DATASET_PATH, 'val', 'pineapple'), 1
    )
    
    # Load test data
    test_corn_images, test_corn_class, test_corn_bbox = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'corn'), 0
    )
    test_pineapple_images, test_pineapple_class, test_pineapple_bbox = load_images_from_folder(
        os.path.join(DATASET_PATH, 'test', 'pineapple'), 1
    )
    
    # Combine training and validation data
    X_train = np.concatenate([train_corn_images, train_pineapple_images, 
                             val_corn_images, val_pineapple_images])
    y_train_class = np.concatenate([train_corn_class, train_pineapple_class,
                                   val_corn_class, val_pineapple_class])
    y_train_bbox = np.concatenate([train_corn_bbox, train_pineapple_bbox,
                                  val_corn_bbox, val_pineapple_bbox])
    
    X_test = np.concatenate([test_corn_images, test_pineapple_images])
    y_test_class = np.concatenate([test_corn_class, test_pineapple_class])
    y_test_bbox = np.concatenate([test_corn_bbox, test_pineapple_bbox])
    
    # Shuffle training data
    train_indices = np.random.permutation(len(X_train))
    X_train = X_train[train_indices]
    y_train_class = y_train_class[train_indices]
    y_train_bbox = y_train_bbox[train_indices]
    
    print(f"Training set: {len(X_train)} images")
    print(f"Test set: {len(X_test)} images")
    print(f"Class labels shape: {y_train_class.shape}")
    print(f"Bbox labels shape: {y_train_bbox.shape}")
    
    return X_train, y_train_class, y_train_bbox, X_test, y_test_class, y_test_bbox

# Load the dataset with localization labels
X_train, y_train_class, y_train_bbox, X_test, y_test_class, y_test_bbox = load_dataset()

print(f"\nDataset shapes:")
print(f"X_train: {X_train.shape}")
print(f"y_train_class: {y_train_class.shape}")
print(f"y_train_bbox: {y_train_bbox.shape}")
print(f"X_test: {X_test.shape}")
print(f"y_test_class: {y_test_class.shape}")
print(f"y_test_bbox: {y_test_bbox.shape}")

def create_cnn_model_with_localization():
    """Create a CNN model with both classification and localization outputs"""
    print("🏗️ Creating CNN model with localization...")

    # Input layer
    input_layer = tf.keras.Input(shape=(IMG_SIZE, IMG_SIZE, 3))

    # First convolutional block
    x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu')(input_layer)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)

    # Second convolutional block
    x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)

    # Third convolutional block
    x = tf.keras.layers.Conv2D(128, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)

    # Fourth convolutional block
    x = tf.keras.layers.Conv2D(256, (3, 3), activation='relu')(x)
    x = tf.keras.layers.MaxPooling2D((2, 2))(x)

    # Flatten and shared dense layers
    x = tf.keras.layers.Flatten()(x)
    x = tf.keras.layers.Dense(512, activation='relu')(x)
    x = tf.keras.layers.Dropout(0.5)(x)
    x = tf.keras.layers.Dense(256, activation='relu')(x)
    x = tf.keras.layers.Dropout(0.5)(x)

    # Classification output (2 classes: corn, pineapple)
    class_output = tf.keras.layers.Dense(2, activation='softmax', name='class_output')(x)

    # Bounding box output: 4 coordinates in YOLO format (center_x, center_y, width, height)
    bbox_output = tf.keras.layers.Dense(4, activation='sigmoid', name='bbox_output')(x)

    # Final model with multiple outputs
    model = tf.keras.Model(inputs=input_layer, outputs=[class_output, bbox_output])
    
    # Compile model with multiple losses
    model.compile(
        optimizer='adam',
        loss={
            'class_output': 'categorical_crossentropy',
            'bbox_output': 'mse'
        },
        loss_weights={
            'class_output': 2.0,  # Prioritize classification
            'bbox_output': 0.5    # Lower weight for bbox since it's synthetic
        },
        metrics={
            'class_output': 'accuracy',
            'bbox_output': 'mae'
        }
    )

    print("✅ Model with localization created!")
    return model

# Create the model
model = create_cnn_model_with_localization()
model.summary()

print("🚀 Training CNN classifier with localization...")

# Train model with multiple outputs
history = model.fit(
    X_train, 
    {
        'class_output': y_train_class,
        'bbox_output': y_train_bbox
    },
    batch_size=32,
    epochs=10,
    validation_split=0.2,
    verbose=1
)

print("✅ Training completed!")

# Plot training history for both outputs
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Classification accuracy
axes[0, 0].plot(history.history['class_output_accuracy'], label='Training Accuracy')
axes[0, 0].plot(history.history['val_class_output_accuracy'], label='Validation Accuracy')
axes[0, 0].set_title('Classification Accuracy')
axes[0, 0].set_xlabel('Epoch')
axes[0, 0].set_ylabel('Accuracy')
axes[0, 0].legend()
axes[0, 0].grid(True)

# Classification loss
axes[0, 1].plot(history.history['class_output_loss'], label='Training Loss')
axes[0, 1].plot(history.history['val_class_output_loss'], label='Validation Loss')
axes[0, 1].set_title('Classification Loss')
axes[0, 1].set_xlabel('Epoch')
axes[0, 1].set_ylabel('Loss')
axes[0, 1].legend()
axes[0, 1].grid(True)

# Bounding box MAE
axes[1, 0].plot(history.history['bbox_output_mae'], label='Training MAE')
axes[1, 0].plot(history.history['val_bbox_output_mae'], label='Validation MAE')
axes[1, 0].set_title('Bounding Box MAE')
axes[1, 0].set_xlabel('Epoch')
axes[1, 0].set_ylabel('MAE')
axes[1, 0].legend()
axes[1, 0].grid(True)

# Bounding box loss
axes[1, 1].plot(history.history['bbox_output_loss'], label='Training Loss')
axes[1, 1].plot(history.history['val_bbox_output_loss'], label='Validation Loss')
axes[1, 1].set_title('Bounding Box Loss')
axes[1, 1].set_xlabel('Epoch')
axes[1, 1].set_ylabel('Loss')
axes[1, 1].legend()
axes[1, 1].grid(True)

plt.tight_layout()
plt.show()

print("📊 Evaluating classifier with localization...")

# Get predictions for both outputs
class_predictions, bbox_predictions = model.predict(X_test)
y_pred = np.argmax(class_predictions, axis=1)
y_true = np.argmax(y_test_class, axis=1)

# Calculate classification accuracy
accuracy = np.mean(y_pred == y_true)
print(f"Classification Accuracy: {accuracy:.4f}")

# Print classification report
print("\nClassification Report:")
# Get unique classes present in the data
unique_classes = np.unique(np.concatenate([y_true, y_pred]))
active_class_names = [CLASS_NAMES[i] for i in unique_classes]
print(classification_report(y_true, y_pred, labels=unique_classes, target_names=active_class_names))

# Calculate bounding box MAE
bbox_mae = np.mean(np.abs(bbox_predictions - y_test_bbox))
print(f"\nBounding Box MAE: {bbox_mae:.4f}")

# Confusion matrix
cm = confusion_matrix(y_true, y_pred, labels=unique_classes)
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
           xticklabels=active_class_names, yticklabels=active_class_names)
plt.title('Confusion Matrix')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

def test_random_predictions_with_localization(model, X_test, y_test_class, y_test_bbox, num_tests=10):
    """Test random predictions with localization"""
    print(f"🔍 Testing {num_tests} random predictions with localization...")

    indices = np.random.choice(len(X_test), num_tests, replace=False)
    correct_class = 0
    bbox_errors = []

    for i, idx in enumerate(indices):
        # Get predictions
        input_data = np.expand_dims(X_test[idx], axis=0)
        class_pred, bbox_pred = model.predict(input_data, verbose=0)
        
        # Class prediction
        predicted_class_idx = np.argmax(class_pred[0])
        true_class_idx = np.argmax(y_test_class[idx])
        predicted_class = CLASS_NAMES[predicted_class_idx]
        true_class = CLASS_NAMES[true_class_idx]
        
        # Class accuracy
        is_class_correct = predicted_class_idx == true_class_idx
        if is_class_correct:
            correct_class += 1
        
        # Bounding box error (Mean Absolute Error)
        bbox_error = np.mean(np.abs(bbox_pred[0] - y_test_bbox[idx]))
        bbox_errors.append(bbox_error)
        
        # Confidence
        confidence = np.max(class_pred[0])

        print(f"Test {i+1:2d}: True={true_class:11s} | Pred={predicted_class:11s} | Conf={confidence:.2f} | BBox_MAE={bbox_error:.3f} | {'✓' if is_class_correct else '✗'}")

    class_accuracy = correct_class / num_tests
    avg_bbox_error = np.mean(bbox_errors)
    
    print(f"\nRandom test results:")
    print(f"Classification accuracy: {class_accuracy:.2f} ({correct_class}/{num_tests})")
    print(f"Average bounding box MAE: {avg_bbox_error:.3f}")
    
    return class_accuracy, avg_bbox_error

# Test random predictions
test_random_predictions_with_localization(model, X_test, y_test_class, y_test_bbox, num_tests=15)

# Show output format example
print("\n" + "="*50)
print("📋 MODEL OUTPUT FORMAT EXAMPLE")
print("="*50)

# Get a single prediction to show format
sample_idx = 0
sample_input = np.expand_dims(X_test[sample_idx], axis=0)
class_pred, bbox_pred = model.predict(sample_input, verbose=0)

# Show classification output
pred_class_idx = np.argmax(class_pred[0])
pred_class = CLASS_NAMES[pred_class_idx]
confidence = np.max(class_pred[0])

# Show bounding box output (YOLO format)
pred_bbox = bbox_pred[0]

print(f"Classification Output:")
print(f"  pred_class = \"{pred_class}\"")
print(f"  confidence = {confidence:.3f}")
print(f"\nBounding Box Output (YOLO format):")
print(f"  pred_bbox = [{pred_bbox[0]:.3f}, {pred_bbox[1]:.3f}, {pred_bbox[2]:.3f}, {pred_bbox[3]:.3f}]")
print(f"  # Format: [center_x, center_y, width, height] - all normalized (0-1)")
print(f"\nComplete Output:")
print(f"  pred_class = \"{pred_class}\"")
print(f"  pred_bbox = [{pred_bbox[0]:.3f}, {pred_bbox[1]:.3f}, {pred_bbox[2]:.3f}, {pred_bbox[3]:.3f}]  # [cx, cy, w, h]")

def visualize_predictions_with_bbox(model, X_test, y_test_class, y_test_bbox, num_samples=6):
    """Visualize predictions with bounding boxes"""
    indices = np.random.choice(len(X_test), num_samples, replace=False)
    
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    axes = axes.flatten()
    
    for i, idx in enumerate(indices):
        # Get predictions
        input_data = np.expand_dims(X_test[idx], axis=0)
        class_pred, bbox_pred = model.predict(input_data, verbose=0)
        
        # Class prediction
        predicted_class_idx = np.argmax(class_pred[0])
        true_class_idx = np.argmax(y_test_class[idx])
        predicted_class = CLASS_NAMES[predicted_class_idx]
        true_class = CLASS_NAMES[true_class_idx]
        confidence = np.max(class_pred[0])
        
        # Show image
        axes[i].imshow(X_test[idx])
        
        # Convert YOLO format to rectangle coordinates for visualization
        def yolo_to_rect(bbox_yolo, img_size):
            center_x, center_y, width, height = bbox_yolo
            x1 = (center_x - width/2) * img_size
            y1 = (center_y - height/2) * img_size
            w = width * img_size
            h = height * img_size
            return x1, y1, w, h
        
        # Draw true bounding box (green) - YOLO format
        true_bbox = y_test_bbox[idx]
        true_x1, true_y1, true_w, true_h = yolo_to_rect(true_bbox, IMG_SIZE)
        axes[i].add_patch(plt.Rectangle((true_x1, true_y1), true_w, true_h, 
                                       fill=False, edgecolor='green', linewidth=2, label='True'))
        
        # Draw predicted bounding box (red) - YOLO format
        pred_bbox = bbox_pred[0]
        pred_x1, pred_y1, pred_w, pred_h = yolo_to_rect(pred_bbox, IMG_SIZE)
        axes[i].add_patch(plt.Rectangle((pred_x1, pred_y1), pred_w, pred_h, 
                                       fill=False, edgecolor='red', linewidth=2, label='Pred'))
        
        # Title with class prediction
        color = 'green' if predicted_class == true_class else 'red'
        axes[i].set_title(f"True: {true_class}\nPred: {predicted_class} ({confidence:.2f})", color=color)
        axes[i].axis('off')
        
        if i == 0:  # Add legend to first subplot
            axes[i].legend(loc='upper right')
    
    plt.tight_layout()
    plt.show()

# Visualize predictions with bounding boxes
visualize_predictions_with_bbox(model, X_test, y_test_class, y_test_bbox, num_samples=6)

def predict_single_image(model, image_path, show_visualization=True):
    """Predict class and bounding box for a single image"""
    try:
        # Load and preprocess the image
        img = load_img(image_path, target_size=(IMG_SIZE, IMG_SIZE))
        img_array = img_to_array(img) / 255.0  # Normalize to [0, 1]
        img_input = np.expand_dims(img_array, axis=0)
        
        # Make prediction
        class_pred, bbox_pred = model.predict(img_input, verbose=0)
        
        # Process classification output
        pred_class_idx = np.argmax(class_pred[0])
        pred_class = CLASS_NAMES[pred_class_idx]
        confidence = np.max(class_pred[0])
        
        # Process bounding box output (YOLO format)
        pred_bbox = bbox_pred[0]
        center_x, center_y, width, height = pred_bbox
        
        # Print results
        print(f"📸 Image: {image_path}")
        print(f"🏷️  Classification:")
        print(f"   pred_class = \"{pred_class}\"")
        print(f"   confidence = {confidence:.3f}")
        print(f"📦 Bounding Box (YOLO format):")
        print(f"   pred_bbox = [{center_x:.3f}, {center_y:.3f}, {width:.3f}, {height:.3f}]")
        print(f"   # Format: [center_x, center_y, width, height] - normalized (0-1)")
        
        # Visualization
        if show_visualization:
            plt.figure(figsize=(8, 8))
            plt.imshow(img)
            
            # Convert YOLO format to rectangle for visualization
            x1 = (center_x - width/2) * IMG_SIZE
            y1 = (center_y - height/2) * IMG_SIZE
            w = width * IMG_SIZE
            h = height * IMG_SIZE
            
            # Draw bounding box
            plt.gca().add_patch(plt.Rectangle((x1, y1), w, h, 
                                            fill=False, edgecolor='red', linewidth=3))
            
            # Add text annotation
            plt.text(x1, y1-5, f'{pred_class} ({confidence:.2f})', 
                    bbox=dict(boxstyle='round', facecolor='red', alpha=0.8),
                    fontsize=12, color='white', weight='bold')
            
            plt.title(f'Prediction: {pred_class} (Confidence: {confidence:.3f})', fontsize=14)
            plt.axis('off')
            plt.tight_layout()
            plt.show()
        
        # Return results in the requested format
        return {
            'pred_class': pred_class,
            'pred_bbox': [center_x, center_y, width, height],
            'confidence': confidence,
            'class_probabilities': class_pred[0].tolist()
        }
        
    except Exception as e:
        print(f"❌ Error processing image {image_path}: {e}")
        return None

# Example usage - Test on a single image
print("🔍 SINGLE IMAGE INFERENCE DEMO")
print("=" * 40)

# Method 1: Use your custom image path
custom_image_path = r"D:\Backup\Recap\Test\test.png"
# Check if custom image exists, otherwise use dataset image
if os.path.exists(custom_image_path):
    test_image_path = custom_image_path
    print(f"Using custom image: {test_image_path}")
else:
    print("Custom image not found, searching dataset...")
    test_image_path = None
    for split in ['test', 'val', 'train']:
        for class_name in ['corn', 'pineapple']:
            class_dir = os.path.join(DATASET_PATH, split, class_name)
            if os.path.exists(class_dir):
                image_files = [f for f in os.listdir(class_dir) 
                              if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
                if image_files:
                    test_image_path = os.path.join(class_dir, image_files[0])
                    break
        if test_image_path:
            break
    
    if test_image_path:
        print(f"Using dataset image: {test_image_path}")

if test_image_path:
    print(f"Testing with: {test_image_path}")
    result = predict_single_image(model, test_image_path)
    
    if result:
        print(f"\n📋 FORMATTED OUTPUT:")
        print(f"pred_class = \"{result['pred_class']}\"")
        print(f"pred_bbox = {result['pred_bbox']}  # [cx, cy, w, h]")
else:
    print("❌ No valid image found")

print("\n" + "="*50)
print("💡 TO TEST YOUR OWN IMAGE:")
print("Simply change the custom_image_path variable above:")
print("\n# Example:")
print("# custom_image_path = r'C:\\path\\to\\your\\image.jpg'")
print("# Then re-run this cell")
print("="*50)

# Save model
model.save("fruit_classifier_with_localization.h5")
print("Model saved as 'fruit_classifier_with_localization.h5'")

print(f"\n🎉 TensorFlow fruit classifier with localization completed!")
print(f"Final classification accuracy: {accuracy:.4f}")
print(f"Final bounding box MAE: {bbox_mae:.4f}")