# Remove Xbox and Phone Link from Windows 11
# Run this script as Administrator

Write-Host "Starting removal of Xbox and Phone Link components..." -ForegroundColor Yellow

# Function to remove AppX packages
function Remove-AppXPackages {
    param([string]$PackageName)
    
    Write-Host "Removing $PackageName packages..." -ForegroundColor Cyan
    
    # Remove for current user
    Get-AppxPackage -Name "*$PackageName*" | Remove-AppxPackage -ErrorAction SilentlyContinue
    
    # Remove for all users
    Get-AppxPackage -AllUsers -Name "*$PackageName*" | Remove-AppxPackage -AllUsers -ErrorAction SilentlyContinue
    
    # Remove provisioned packages (prevents reinstall)
    Get-AppxProvisionedPackage -Online | Where-Object DisplayName -like "*$PackageName*" | Remove-AppxProvisionedPackage -Online -ErrorAction SilentlyContinue
}

# Xbox related packages
$XboxPackages = @(
    "Microsoft.XboxApp",
    "Microsoft.XboxGameOverlay",
    "Microsoft.XboxGamingOverlay",
    "Microsoft.XboxIdentityProvider",
    "Microsoft.XboxSpeechToTextOverlay",
    "Microsoft.Xbox.TCUI",
    "Microsoft.GamingApp",
    "Microsoft.GamingServices"
)

# Phone Link packages
$PhoneLinkPackages = @(
    "Microsoft.YourPhone",
    "Microsoft.PhoneLink"
)

# Remove Xbox packages
Write-Host "`nRemoving Xbox packages..." -ForegroundColor Green
foreach ($package in $XboxPackages) {
    Remove-AppXPackages -PackageName $package
}

# Remove Phone Link packages
Write-Host "`nRemoving Phone Link packages..." -ForegroundColor Green
foreach ($package in $PhoneLinkPackages) {
    Remove-AppXPackages -PackageName $package
}

# Disable Xbox Game Bar via Registry
Write-Host "`nDisabling Xbox Game Bar..." -ForegroundColor Cyan
$GameBarPath = "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\GameDVR"
if (!(Test-Path $GameBarPath)) {
    New-Item -Path $GameBarPath -Force | Out-Null
}
Set-ItemProperty -Path $GameBarPath -Name "AppCaptureEnabled" -Value 0 -Type DWord
Set-ItemProperty -Path $GameBarPath -Name "GameDVR_Enabled" -Value 0 -Type DWord

$GameBarPath2 = "HKCU:\System\GameConfigStore"
if (!(Test-Path $GameBarPath2)) {
    New-Item -Path $GameBarPath2 -Force | Out-Null
}
Set-ItemProperty -Path $GameBarPath2 -Name "GameDVR_Enabled" -Value 0 -Type DWord

# Disable Game Mode
Write-Host "Disabling Game Mode..." -ForegroundColor Cyan
$GameModePath = "HKCU:\SOFTWARE\Microsoft\GameBar"
if (!(Test-Path $GameModePath)) {
    New-Item -Path $GameModePath -Force | Out-Null
}
Set-ItemProperty -Path $GameModePath -Name "AutoGameModeEnabled" -Value 0 -Type DWord

# Remove Xbox services (optional - be careful)
Write-Host "`nDisabling Xbox services..." -ForegroundColor Yellow
$XboxServices = @(
    "XblAuthManager",
    "XblGameSave",
    "XboxGipSvc",
    "XboxNetApiSvc"
)

foreach ($service in $XboxServices) {
    try {
        $svc = Get-Service -Name $service -ErrorAction SilentlyContinue
        if ($svc) {
            Stop-Service -Name $service -Force -ErrorAction SilentlyContinue
            Set-Service -Name $service -StartupType Disabled -ErrorAction SilentlyContinue
            Write-Host "Disabled service: $service" -ForegroundColor Green
        }
    }
    catch {
        Write-Host "Could not disable service: $service" -ForegroundColor Red
    }
}

# Clean up Start Menu tiles
Write-Host "`nCleaning up Start Menu..." -ForegroundColor Cyan
$StartMenuPath = "$env:LOCALAPPDATA\Packages\Microsoft.Windows.StartMenuExperienceHost_cw5n1h2txyewy\LocalState"
if (Test-Path $StartMenuPath) {
    Remove-Item "$StartMenuPath\start*.bin" -Force -ErrorAction SilentlyContinue
}

Write-Host "`nRemoval process completed!" -ForegroundColor Green
Write-Host "You may need to restart your computer for all changes to take effect." -ForegroundColor Yellow
Write-Host "`nNote: Some components may be reinstalled during Windows updates." -ForegroundColor Red

# Optional: Create a scheduled task to prevent reinstallation
$CreateTask = Read-Host "`nDo you want to create a scheduled task to prevent reinstallation? (y/n)"
if ($CreateTask -eq 'y' -or $CreateTask -eq 'Y') {
    Write-Host "Creating prevention task..." -ForegroundColor Cyan
    
    $TaskAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-WindowStyle Hidden -Command `"Get-AppxPackage *xbox*,*phonelink*,*yourphone* | Remove-AppxPackage -ErrorAction SilentlyContinue`""
    $TaskTrigger = New-ScheduledTaskTrigger -AtLogOn
    $TaskSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable
    $TaskPrincipal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
    
    Register-ScheduledTask -TaskName "RemoveXboxPhoneLink" -Action $TaskAction -Trigger $TaskTrigger -Settings $TaskSettings -Principal $TaskPrincipal -Description "Prevents Xbox and Phone Link reinstallation" -Force
    
    Write-Host "Scheduled task created successfully!" -ForegroundColor Green
}

Write-Host "`nScript execution completed. Press any key to exit..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
